import React from 'react';

const WidgetRatings = ({rating_count,supplier_rating }) => {

    const rate_width = (supplier_rating * 100) / 5;
    const width_style = `${rate_width}%`;
    return (<div id='sellr_rtng' className="sellercls sllrTph1F" style={{ cursor: 'pointer'}} >
                
        <div className="sRtF sRtcls pr">
            <div className="flsRtF abslt" style={{ width: width_style }}><span>&#x2605;&#x2605;&#x2605;&#x2605;&#x2605;</span></div>
            <div><span>&#x2605;&#x2605;&#x2605;&#x2605;&#x2605;</span></div>
        </div>&nbsp;
        <span className="stWid">{supplier_rating}</span>&nbsp;
        {(rating_count > 0) ? <span className='tchov1F'>(<span className='tcundf'>{rating_count}</span>)</span>
            : <></>}
    </div>);
};

export default WidgetRatings;
