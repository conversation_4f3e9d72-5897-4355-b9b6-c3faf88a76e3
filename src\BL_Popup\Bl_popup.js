import React, { memo, useState } from "react";
import './leftSecBL.css';
import { useGlobalState } from "../context/store";
import BlLeftSec from "./BlLeftSec";
import { readCookieREC, isset, getparamValREC, isInactBL, stopBgScrollREC, imeqglval, Eventtracking,isPresent,isBlRevampd } from "../common/formCommfun";
import Thankyoumain from "../Thankyou/Thankyoumain";
import Contactdtl from "../ContactDetails/ContactDtl";
import { useEffect } from "react";
import ReqdtlBL from "./ReqdtlBL";
import User_ver from "../OTP/User_ver";
import Bl_Login from "./Bl_Login";
import MoreReqDet from "../MoreReqDet";
import IntGenApi from "../main/IntGenApi";
import IsqDtlBl from "./IsqDtlBl";
import callMiniDetAPI from "../MinidtlsAPI";
import CatsImgRecom from "../image_enq/CatsImgRecom";
import ProdsInactive from "../image_enq/ProdsInactive";
import Bl_ProdSrch from "./Bl_ProdSrch";
import RenderAd from "../common/RenderAd";
import { callAdvSearchAPI } from "../common/AdvSearchAPI";

export function Form_BLpopupMemo({ id, close_fun, form_param}) {
  const { state, dispatch } = useGlobalState();
  useEffect(() => {
    dispatch({ type: "frscr", payload: { frscr: 2 } });
    const ismshexist = readCookieREC("ImeshVisitor");
    if (ismshexist == null) {
      dispatch({ type: "Imeshform", payload: { Imeshform: false } });
      dispatch({ type: "frscr", payload: { frscr: 1 } });
    } else {
      dispatch({ type: "Imeshform", payload: { Imeshform: true } });
    }
    dispatch({ type: "IsqformBL", payload: { IsqformBL: false } });
    dispatch({ type: "RDform", payload: { RDform: false } });
    dispatch({ type: "RDformBL", payload: { RDformBL: false } });
    dispatch({ type: "MrEnrForm", payload: { MrEnrForm: false } });
    dispatch({ type: "thankyou", payload: { thankyou: false } });
  }, []);
  const [isLoaded, setIsLoaded] = useState(false);
  const [fn, setFn] = useState("");
  const [em, setEm] = useState("");
  const [mb, setMb] = useState("");
  const [iso, setIso] = useState("");
  const [mdresct, setMdresct] = useState("");
  const [phext, setPhext] = useState("");
  const [mindet, setmindet] = useState(null);
  const [showThankyou, setShowThankyou] = useState(false);
  const [err_msg, seterr_msg] = useState("");
  const [blsearchval , setblsearchval] = useState("");
  const [advAPIres , setAdvAPIres] = useState([]);
  const [shouldShowThankyouOnClose, setShouldShowThankyouOnClose] = useState(false);
  const [headProd,setHeadProd]= useState((form_param.prodDispName || form_param.prodName || ''));

  window.isBLFormOpen = true;
  stopBgScrollREC();
  window.otpcountRes = 0;
  window.otpcountReser = 0;


  useEffect(() => {
    const fetchMiniDetAPI = async () => {
      const imesh = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor") : "";
      let mdtlres = null;
      let city = "";
      let gliddd = getparamValREC(imesh, "glid");
      try {
        mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
      }
      catch (e) {
        mdtlres = null;
      }
      if (mdtlres && mdtlres[gliddd]) {
        city = isPresent(mdtlres[gliddd].Response.Data[0]) ? mdtlres[gliddd].Response.Data[0] : "" ;
        dispatch({ type: 'md_resp', payload: { md_resp: mdtlres } });
        setmindet(mdtlres[gliddd]);
      } else {
        if (gliddd != "") {
          const data = await callMiniDetAPI(form_param);
          city = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0]) ? data.Response.Data[0] : "";
          if (isset(() => data) && data && data.Response && data.Response.Data) { 
            setmindet(data); 
            dispatch({ type: 'md_resp', payload: { md_resp: data.md_resp } });

          } else { 
            setmindet("No Response From Service"); 
          }
         
        }
      }
      city = getparamValREC(imesh, "ctid") ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : city ;
      setMdresct(city != "" ? "1" : "");
      const visitorFn = getparamValREC(imesh, "fn") || state.UserData.fn;
      // const visitorEm = getparamValREC(imesh, "em") || state.UserData.em;
      const visitorMb = getparamValREC(imesh, "mb1") || state.UserData.mb1;
      const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
      const phext = getparamValREC(imesh, "phcc") || state.UserData.phcc;

      setFn(visitorFn);
      // setEm(visitorEm);
      setMb(visitorMb);
      setIso(visitorIso);
      setPhext(phext);
      const nec_con =
        ((visitorFn == "" || city == "") &&
          visitorIso == "IN") ||
          (visitorMb == "" && visitorIso != "IN")
          ? true
          : false;
      const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
      const otp_con = uv != "V" && visitorIso == "IN" ? true : false;
      if (nec_con == true) {
        dispatch({ type: "NECcon", payload: { NECcon: true } });
      } else {
        dispatch({ type: "NECcon", payload: { NECcon: false } });
        if (otp_con == true) {
          dispatch({ type: "OTPcon", payload: { OTPcon: true } });
        }
      }
      setIsLoaded(true);
    }
    fetchMiniDetAPI();
  }, [state.Imeshform]);
  useEffect(() => {
    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    const visitorIso = getparamValREC(imesh, "iso") || state.UserData.iso;
    const uv = getparamValREC(imesh, "uv") || state.UserData.uv;
    const otp_con = uv != "V" && visitorIso == "IN" ? true : false;
    if (otp_con == true) {
      dispatch({ type: "OTPcon", payload: { OTPcon: true } });
    } else {
      dispatch({ type: "OTPcon", payload: { OTPcon: false } });
      if (!state.searchshown) { dispatch({ type: "frscr", payload: { frscr: 1 } }); }
      dispatch({ type: "IsqformBL", payload: { IsqformBL: true } });
    }
    if (!isLoaded) {
      setIsLoaded(true);
    }
  }, [state.Imeshform]);
  useEffect(() => {
    dispatch({ type: "postreq", payload: { postreq: 0 } });
    const imesh = isset(() => readCookieREC("ImeshVisitor"))
      ? readCookieREC("ImeshVisitor")
      : "";
    id == "0901" && imesh != "" ? IntGenApi(form_param) : "";
    if((!form_param.blMultiImage || form_param.blMultiImage.length<2)&&(isBlRevampd(form_param))){
      fetchAdvSearchData(form_param);
    }
  }, [form_param]);

  
  const fetchAdvSearchData = async (form_param) => {
    const results = await callAdvSearchAPI(form_param,'bl');
    setAdvAPIres(results);
    // console.log(results);

      const largeImages = results.map(item => {
        if (item && item.fields && item.fields.large_image) {
          return item.fields.large_image;
        }
        return null;
      }).filter(img => img !== null); // Remove any null values

      if (largeImages.length > 0) {
        form_param.blMultiImage = largeImages;
      }
      
    
  };

  const handleKeyPress = (event) => {
    if (event.keyCode === 27) {
      handleClose('EscapeKeyPressed');
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [form_param]);

  const handleClose = (source = '') => {
    Eventtracking("CS" + window.screencount + "|" + state.currentscreen + "|" + source, state.prevtrack, form_param, false);
    const imeqarr = imeqglval();
    if (!imeqarr.BL && form_param.formType == 'BL') {
      window.showskipBut = true;
    }
    else { 
      window.showskipBut = false;
    }
    if (!showThankyou && (state.postreq != undefined && state.postreq != 0) && !state.thankyou) {
      setShowThankyou(true);
    } else {
      close_fun();
    }
    dispatch({ type: 'thankyou', payload: { thankyou: false } });
  };
  useEffect(() => {
    if (state.OTPcon && !state.NECcon) {
      dispatch({ type: "frscr", payload: { frscr: 2 } });
      // dispatch({ type: 'searchshown', payload: { searchshown:  false} });
    } else if (!state.searchshown) {
      dispatch({ type: "frscr", payload: { frscr: 1 } });
    }
  }, [state.OTPcon, state.NECcon]);
  if (!isLoaded) {
    // Render a loading state or nothing until the data is loaded
    return null;
  }

  return (
    <React.Fragment>
      <div className="ber-frwrap" id={`t${id}_bewrapper`} style={{zIndex:"999"}}>
        <div
          className="blckbg"
          id={`t${id}_blkwrap`}
          onClick={() => handleClose("OutsideClicked")}
        >X</div>
        <div className={`frmcont ${isBlRevampd(form_param) ? 'blNew' : ''}`}>
          {state.thankyou || showThankyou ? (
            <Thankyoumain form_param={form_param} close_fun={close_fun} />
          ) : (
            <div
              className={`ber-mcontbl oEq_r blHW bezid blder br24${isInactBL(form_param) ? 'imgscroll' : ''}`} style={{display:"block"}}
            >
              <div className="idsf">
                <BlLeftSec form_param={form_param}  headProd={headProd} setHeadProd={setHeadProd}/>
                <div id={`t${id}_leftR`} className="ber-RscBL ber-frmpop btPd br24R">
                  <div id={`t${id}_cls`} className="ber-cls-rec cp" onClick={() => handleClose("CrossButtonClicked")}>X</div>
                  {
                    state.frscr == 1 && !state.openinlnBLPopup && !(state.OTPcon && !state.NECcon) &&
                    <Bl_ProdSrch form_param={form_param} layout={''} name={"Enter Product/Service name"} err_msg={err_msg} seterr_msg={seterr_msg} setblsearchval= {setblsearchval}  headProd={headProd} setHeadProd={setHeadProd} fetchAdvSearchData={fetchAdvSearchData}/>
                  }
                  {((mindet) || state.Imeshform == false) && <div id={`t${id}_rightsection`}>
                    {
                      !state.Imeshform ? (
                        <Bl_Login id={id} form_param={form_param} err_msg={err_msg} seterr_msg={seterr_msg} blsearchval={blsearchval} />
                      ) :
                        state.NECcon == true ?
                          (
                            <Contactdtl
                              fn={fn}
                              em={em}
                              mb={mb}
                              ctid={mdresct}
                              iso={iso}
                              phext={phext}
                              form_param={form_param}
                              MoreReq={false}
                              blsearchval={blsearchval}
                              seterr_msg={seterr_msg}
                            />
                          )
                          : state.OTPcon == true ? (
                            <User_ver form_param={form_param} />
                          )
                            : state.IsqformBL == true ? (
                              <IsqDtlBl form_param={form_param} blsearchval={blsearchval} seterr_msg={seterr_msg}/>
                            ) : state.RDformBL == true ? (
                              <ReqdtlBL form_param={form_param} blsearchval={blsearchval} seterr_msg={seterr_msg}/>
                            )
                              : ("")
                    }
                    {state.MrEnrForm && <MoreReqDet
                      md_resp={state.md_resp} form_param={form_param}
                    />}
                  </div>}
                </div>

              </div>
              {isInactBL(form_param) ? <RenderAd adUnitPath="/3047175/Desktop_Enq_BL_Form_Bottom_Ad-InactiveBL" sizes={[[520, 90], [728, 90], [120, 90], [220, 90]]} divId="div-gpt-ad-1742970371516-0" style={{ textAlign: 'center' }} /> : ''}
              {isInactBL(form_param) ? <ProdsInactive form_param={form_param} /> : ''}
              {isInactBL(form_param) ? <CatsImgRecom form_param={form_param} /> : ''}
            </div>
          )}
        </div>
      </div>
    </React.Fragment>
  );
}
const BLpopup_form = memo(Form_BLpopupMemo);
export default BLpopup_form;
