.phoneParent {
    position: relative;
}

/* Input container */
.input-container {
    /* display: flex; */
    /* align-items: center; */
    width: 331px;
}

/* Country code input (read-only) */
input[type="text"] {
    /* text-align: center; */
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
}
/* Phone number input */
input[type="text"][name="phoneNumber"] {
    width: 100%;
    padding: 5px;
    font-size: 14px;
    color: #333;
    text-align: left;
    /* padding-left: 62px; */
    height: 42px;
    border: 1px solid #ccc;
    transition: border-color 0.2s ease-in-out; /* Add transition for smooth border color change */
    border-radius: 3px !important;
    margin-top: 10px;
}

input[type="text"][name="phext"] {
    position: absolute;
    border-radius: 3px 0 0 3px !important;
    left: 1px;
    top: 1px;
    height: 40px;
    border: none;
    border-right: 1px solid #c9c6c6 !important;
    width: 52px;
    text-align: center;
}

/* Focus state for phone number input */
input[type="text"][name="phoneNumber"]:focus {
    outline: none; /* Remove default focus outline */
    border-color: #4458a7; /* Change border color when focused */
}
.input-container label {
    /* position: absolute;
    transition: all 0.2s;
    pointer-events: none;
    transform: translate3d(55px, 0, 0) scale(1);
    transform-origin: left top; */
    font-size: 14px;
    color: #888;
    /* z-index: 1;
    padding: 5px 10px; */
    text-align: left;
    /* margin-right: 1px;
    background: 0 0; */
    font-weight: 400;
}

/* Active label state */
.input-container label.active {
    transform: translate(55px, -20px);
    font-size: 12px;
    color: #4458a7;;
    line-height: 13px;
    display: block;
    font-weight: 400;
    padding: 5px 5px;
    background-color: #f9f9f9;
}