.phoneParent {
    position: relative;
}

/* Input container */
.input-container {
    width: 331px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Country code input (read-only) */
input[type="text"] {
    /* text-align: center; */
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
}
/* Phone number input */
input[type="text"][name="phoneNumber"] {
    width: 100%;
    font-size: 14px;
    color: #333;
    text-align: left;
    padding-left: 6px;
    height: 42px;
    border: 1px solid #ccc;
    transition: border-color 0.2s ease-in-out;
    border-radius: 0 5px 5px 0px;
}

input[type="text"][name="phoneNumber"]:focus {
    outline: none;
}
.input-container:focus-within .country-code-pi {
    outline: none;
    border-color: #266762;
}
.country-flag-pi{
    width: 16px;
    height: 11px;
    background: url(https://utils.imimg.com/imsrchui/imgs/country-v5.png);
    display: inline-block;
    margin-right: 5px;
}
.country-code-pi {
    display: flex;
    align-items: center;
    border: 1px solid #ccc;
    padding: 6px;
    height: 42px;
    transition: border-color 0.2s ease-in-out;
    border-radius: 5px 0px 0px 5px;
    border-right: none;
}
.country-code-value{
    white-space: nowrap;
}