import { readCookieREC } from '../../common/formCommfun';

// API service for fetching price data from reseller API
export default async function fetchPriceData(form_param) {
  // Check authentication before proceeding
  const authToken = readCookieREC("ImeshVisitor");

  if (!authToken) {
    throw new Error('Authentication required to access price data');
  }

  // Extract required parameters from form_param
  const url_orig = form_param.displayImage || '';
  const subcat_id = form_param.catId || '';
  const rawPrice = form_param.price || '';

  // Extract numeric value from price string (e.g., "₹ 90/Piece" -> "90")
  const price = rawPrice ? rawPrice.toString().replace(/[^\d.]/g, '') : '';

  // Extract unit from input price for comparison (e.g., "₹ 90/Piece" -> "piece")
  const extractUnit = (priceString) => {
    if (!priceString) return '';
    const parts = priceString.toString().split('/');
    // Only return unit if there's actually a "/" in the price
    return parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';
  };

  const inputUnit = extractUnit(rawPrice);

  // Validate required parameters
  if (!url_orig || !subcat_id || !price) {
    throw new Error('Missing required parameters: product image, subcategory ID, or price');
  }

  // Extract cache key from URL path after "Default/"
  // Example: https://5.imimg.com/data5/SELLER/Default/2024/12/471471038/XB/BA/BI/8288033/haldi-powder-1000x1000.jpg
  // Extract: 2024/12/471471038/XB/BA/BI/8288033/haldi-powder-1000x1000.jpg
  const extractCacheKey = (imageUrl) => {
    try {
      const defaultIndex = imageUrl.indexOf('/Default/');
      if (defaultIndex !== -1) {
        return imageUrl.substring(defaultIndex + 9); // +9 to skip "/Default/"
      }
      return imageUrl; // fallback to full URL if pattern not found
    } catch (error) {
      return imageUrl;
    }
  };

  const cacheKey = `price_widget_${extractCacheKey(url_orig)}_${subcat_id}_${inputUnit}`;

  // Check session storage for cached data
  try {
    const cachedData = sessionStorage.getItem(cacheKey);
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      // Check if cache is still valid (e.g., within 1 day)
      const cacheTime = parsedData.timestamp || 0;
      const currentTime = Date.now();
      const cacheValidityPeriod = 24 * 60 * 60 * 1000; // 1 day in milliseconds

      if (currentTime - cacheTime < cacheValidityPeriod) {
        // Process cached original API response
        const data = parsedData.data;

        // Continue with the same processing logic as fresh API response
        if (data.status === "200" && data.results && data.results.length > 0) {
          // Transform cached API response
          let products = data.results.map(item => ({
            id: item.DISPLAY_ID,
            name: item.ITEM_NAME,
            price: parseFloat(item.PRICE_SEO) || 0,
            priceDisplay: item.PRICE_F,
            image: item.IMAGE_500X500 || item.IMAGE_250X250 || item.IMAGE_125X125,
            supplier: item.COMPANYNAME,
            location: `${item.CITY_NAME}, ${item.STATE_NAME}`
          }));

          const currentProductId = form_param.pDispId;

          // Apply same filtering logic - only filter by unit if input has a unit
          if (inputUnit) {
            products = products.filter(product => {
              if (!product.priceDisplay) return false;
              const parts = product.priceDisplay.split('/');
              const productUnit = parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';
              return productUnit === inputUnit;
            });
          }
          // If no input unit, show all products (no unit filtering)

          if (currentProductId) {
            products = products.filter(product => product.id != currentProductId);
          }

          // Return processed cached data
          if (products.length > 1) {
            return {
              success: true,
              data: { products: products }
            };
          } else {
            return {
              success: false,
              error: 'Insufficient products for price comparison',
              data: { products: [] }
            };
          }
        }
      } else {
        // Remove expired cache
        sessionStorage.removeItem(cacheKey);
      }
    }
  } catch (error) {
    console.warn('Error reading from session storage:', error);
  }

  // Build server URL like search API
  const webAddressLocation = location.hostname;
  var serverName = webAddressLocation.match(/^dev/)
    ? "//dev-apps.imimg.com/"
    : webAddressLocation.match(/^stg/)
      ? "//stg-apps.imimg.com/"
      : "//dev-apps.imimg.com/";

  var url = serverName + `index.php?r=Newreqform/ResellerData&url_orig=${encodeURIComponent(url_orig)}&subcat_id=${subcat_id}&price=${price}&modid=FORMS&source_page=PDP`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors'
    });

    if (!response.ok) {
      // Track API failure
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': 'API-Response-Failed',
          'eventLabel': `HTTP-${response.status}`,
          'eventValue': 0,
          'non_interaction': 0
        });
      }
      throw new Error('Failed to call API');
    }

    const data = await response.json();

    // Track successful API response
    if (window.imgtm) {
      imgtm.push({
        'event': 'IMEvent-NI',
        'eventCategory': 'Price-Widget',
        'eventAction': 'API-Response-Success',
        'eventLabel': 'Data-Received',
        'eventValue': 1,
        'non_interaction': 0
      });
    }

    // Transform API response to match widget format
    if (data.status === "200" && data.results && data.results.length > 0) {
      const totalProducts = data.results.length;

      // Cache the original API response if more than 1 product
      if (totalProducts > 1) {
        try {
          const cacheData = {
            data: data, // Save original API response
            timestamp: Date.now()
          };
          sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
        } catch (error) {
          console.warn('Error saving to session storage:', error);
        }
      }

      let products = data.results.map(item => ({
        id: item.DISPLAY_ID,
        name: item.ITEM_NAME,
        price: parseFloat(item.PRICE_SEO) || 0, // For comparison only
        priceDisplay: item.PRICE_F, // Full formatted price for display
        image: item.IMAGE_500X500 || item.IMAGE_250X250 || item.IMAGE_125X125,
        supplier: item.COMPANYNAME,
        location: `${item.CITY_NAME}, ${item.STATE_NAME}`
      }));

      // Count products excluding current product (based on pDispId)
      const currentProductId = form_param.pDispId;

      // Filter products to only include those with the same unit as input price
      // Only apply unit filtering if the input price has a unit
      if (inputUnit) {
        products = products.filter(product => {
          if (!product.priceDisplay) return false;

          // Extract unit from PRICE_F (e.g., "₹ 400 / Kg" -> "kg")
          const parts = product.priceDisplay.split('/');
          const productUnit = parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';

          // Compare units (case-insensitive)
          return productUnit === inputUnit;
        });
      }
      // If no input unit, show all products without unit filtering

      // Remove current product from comparison (exclude self)
      if (currentProductId) {
        products = products.filter(product => product.id != currentProductId);
      }

      // Track product count (already excluding current product)
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': 'Product-Count',
          'eventLabel': `Total-${totalProducts}_Other-${products.length}_Unit-${inputUnit || 'none'}`,
          'eventValue': products.length,
          'non_interaction': 0
        });
      }



      // Only show widget if more than 1 product is present after filtering
      if (products.length <= 1) {
        // Track widget formation failure
        if (window.imgtm) {
          imgtm.push({
            'event': 'IMEvent-NI',
            'eventCategory': 'Price-Widget',
            'eventAction': 'Widget-Formation-Failed',
            'eventLabel': `Insufficient-Products-${products.length}`,
            'eventValue': 0,
            'non_interaction': 0
          });
        }

        return {
          success: false,
          error: 'Insufficient products for price comparison',
          data: {
            products: []
          }
        };
      }

      // Track successful widget formation
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': 'Widget-Formation-Success',
          'eventLabel': `Products-${products.length}`,
          'eventValue': 1,
          'non_interaction': 0
        });
      }

      return {
        success: true,
        data: {
          products: products
        }
      };
    } else {
      // Track no data scenario
      if (window.imgtm) {
        imgtm.push({
          'event': 'IMEvent-NI',
          'eventCategory': 'Price-Widget',
          'eventAction': 'API-No-Data',
          'eventLabel': `Status-${data.status || 'unknown'}`,
          'eventValue': 0,
          'non_interaction': 0
        });
      }

      return {
        success: false,
        error: 'No price data available',
        data: {
          products: []
        }
      };
    }

  } catch (error) {
    console.error('Price API Error:', error);

    // Track API error
    if (window.imgtm) {
      imgtm.push({
        'event': 'IMEvent-NI',
        'eventCategory': 'Price-Widget',
        'eventAction': 'API-Error',
        'eventLabel': error.message || 'Unknown-Error',
        'eventValue': 0,
        'non_interaction': 0
      });
    }

    return {
      success: false,
      error: error.message,
      data: {
        products: []
      }
    };
  }
}
