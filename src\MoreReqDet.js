import React, { useState, useEffect, useRef } from "react";
import { useGlobalState } from './context/store';
import Contactdtl from "./ContactDetails/ContactDtl";
import { readCookieREC, getparamValREC, isset, currentISO, Eventtracking ,isPresent} from './common/formCommfun';
import Head_scr from "./common/heading";
import GlusrUpdate from "./Login/GlusrUpdate";
import BlEnqUpdate from "./BlEnqUpdate";
import callMiniDetAPI from "./MinidtlsAPI";
import './BL_Popup/Blcname.css';

export function MoreReqDet({ form_param, md_resp }) {

    const { state, dispatch } = useGlobalState();
    const dynamicKey = md_resp ? Object.keys(md_resp)[0] : "";
    const u_cname = md_resp && md_resp[dynamicKey] && md_resp[dynamicKey].Response && md_resp[dynamicKey].Response.Data && md_resp[dynamicKey].Response.Data[1]
    ? (md_resp[dynamicKey].Response.Data[1] !== '0' ? md_resp[dynamicKey].Response.Data[1] : 0)
    : 0;

    const is_ga = md_resp && md_resp[dynamicKey] && md_resp[dynamicKey].Response && md_resp[dynamicKey].Response.Data && md_resp[dynamicKey].Response.Data[2]
    ? (md_resp[dynamicKey].Response.Data[2] !== '0' ? md_resp[dynamicKey].Response.Data[2] : 0)
    : 0;

    const is_ua = md_resp && md_resp[dynamicKey] && md_resp[dynamicKey].Response && md_resp[dynamicKey].Response.Data && md_resp[dynamicKey].Response.Data[3]
    ? (md_resp[dynamicKey].Response.Data[3] !== '0' ? md_resp[dynamicKey].Response.Data[3] : 0)
    : 0;    
    const visitoriso = state.UserData && state.UserData.iso ? state.UserData.iso : currentISO();
    const handleNextClick = () => {
        let imeshcookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
        const imeshglid = getparamValREC(imeshcookie, 'glid');
        const s_mobile = getparamValREC(imeshcookie, 'mb1') || state.UserData.mb1;
        let user_data = '';
        dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
        let eventcat = "-MoreDetails";
        if (cvalue) {
            eventcat = eventcat + "|Company";
        }
        if (visitoriso == 'IN' && gvalue) {
            eventcat = eventcat + "|GST";
        }
        if (visitoriso != 'IN' && urlvalue) {
            eventcat = eventcat + "|Website";
        }
        Eventtracking("SS" + window.screencount + eventcat, state.prevtrack, form_param, false);
        window.screencount++;
        dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: false } });
        dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack + "-MoreDetails" } });
        BlEnqUpdate(state, form_param);
        dispatch({ type: 'thankyou', payload: { thankyou: true } });
        if (visitoriso == 'IN') {
            user_data = { glid: imeshglid, name: "", s_email: "", s_mobile: s_mobile, country_iso: visitoriso, gst: gvalue, companyName: cvalue }
        }
        else {
            user_data = { glid: imeshglid, name: "", s_email: "", s_mobile: s_mobile, country_iso: visitoriso, url: urlvalue, companyName: cvalue }
        }
        if(user_data.companyName || user_data.gst || user_data.url){
            GlusrUpdate(form_param, user_data);
        }
    };
    const [fn, setFn] = useState('');
    const [em, setEm] = useState('');
    const [mb, setMb] = useState('');
    const [iso, setIso] = useState('');
    const [ctid, setCtid] = useState('');
    const [phext, setPhext] = useState('');
    const [nec_con_add, setNec_con_add] = useState(false);
    const [cvalue, setInputValue] = useState('');
    const [gvalue, setGvalue] = useState('');
    const [urlvalue, setUrlvalue] = useState('');
    const [focusfield, setFocusField] = useState('');

    useEffect(() => {
        function tracking (nec_con ,visitorFn,visitorEm,visitorMb,visitorCity){
            let eventcat = "MoreDetails";
            if (!u_cname) {
                eventcat = eventcat + "|Company";
                setFocusField('Company');
            }
            if (visitoriso == 'IN' && is_ga == 0) {
                eventcat = eventcat + "|GST";
                setFocusField('GST');
            }
            if (visitoriso != 'IN' && is_ua == 0) {
                eventcat = eventcat + "|Website";
                setFocusField('Website');
            }
            if (nec_con && !state.newEnq) {
                if (visitoriso == 'IN') {
                    if (visitorFn == '') {
                        eventcat = eventcat + "|Name";
                    } if (visitorEm == '') {
                        eventcat = eventcat + "|Email";
                    }
                    if (visitorCity == '') {
                        eventcat = eventcat + "|City";
                    }
                } else {
                    if (visitorMb == '') {
                        eventcat = eventcat + "|MobileNumber";
                    }
                }
            }
            Eventtracking("DS" + window.screencount +"-"+ eventcat, state.prevtrack, form_param, false);
            dispatch({ type: 'currentscreen', payload: { currentscreen: eventcat } });
        };
        const fetchMiniDetAPI = async () => {
            const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
            let mdtlres = null;
            let visitorCity = "";
            let gliddd = getparamValREC(imesh, "glid");
            if (getparamValREC(imesh, 'ctid') || state.UserData.ctid == "1") {
                visitorCity = getparamValREC(imesh, 'ctid') || state.UserData.ctid;
            }
            else {
                try {
                    mdtlres = JSON.parse(sessionStorage.getItem("minidtlsres"));
                }
                catch (e) {
                    mdtlres = null;
                }
                if (mdtlres && mdtlres[gliddd] && mdtlres[gliddd].Response && mdtlres[gliddd].Response.Data) {
                    visitorCity = isPresent(mdtlres[gliddd].Response.Data[0]) ? mdtlres[gliddd].Response.Data[0] : "";
                } else {
                    if (gliddd != "") {
                        const data = await callMiniDetAPI(form_param);
                        visitorCity = data && data.Response && data.Response.Data && isPresent(data.Response.Data[0]) ? data.Response.Data[0] : "";
                    }
                }
            }

            const visitorFn = getparamValREC(imesh, 'fn') || state.UserData.fn;
            const visitorEm = getparamValREC(imesh, 'em') || state.UserData.em;
            const visitorMb = getparamValREC(imesh, 'mb1') || state.UserData.mb1;
            const phext = getparamValREC(imesh, 'phcc') || state.UserData.phcc;
            visitorCity = getparamValREC(imesh, 'ctid') ? getparamValREC(imesh, "ctid") : state.UserData.ctid ? state.UserData.ctid : visitorCity;

            setFn(visitorFn);
            setEm(visitorEm);
            setMb(visitorMb);
            setIso(visitoriso);
            setCtid(visitorCity != "" ? "1" : "");
            setPhext(phext);
            const nec_con = ((visitorFn == '' || visitorEm == '' || visitorCity == '') && visitoriso == 'IN') || (visitorMb == '' && visitoriso != 'IN') ? true : false;
            setNec_con_add(nec_con);
            if (u_cname && (!nec_con || state.newEnq)) {
                const isApplicable = (visitoriso == 'IN' && is_ga == 1) || (visitoriso != 'IN' && is_ua == 1);
                if (isApplicable) {
                    dispatch({ type: 'MrEnrForm', payload: { MrEnrForm: false } });
                    BlEnqUpdate(state, form_param);
                    dispatch({ type: 'thankyou', payload: { thankyou: true } });
                } else {
                    tracking(nec_con,visitorFn,visitorEm,visitorMb,visitorCity);
                }
            } else {
                tracking(nec_con,visitorFn,visitorEm,visitorMb,visitorCity);
            }
        };
        fetchMiniDetAPI();
    }, []);

    const getAutoFocus = () => {
        if (!u_cname) return 'company';
        if (visitoriso == 'IN' && is_ga == 0) return 'gst';
        else if (visitoriso != 'IN' && is_ua == 0) return 'website';
        return null;
    };

    const autoFocusField = getAutoFocus();

    const companyRef = useRef(null);
    const gstRef = useRef(null);
    const websiteRef = useRef(null);

    useEffect(() => {
        if (autoFocusField === 'company') companyRef.current?.focus();
        else if (autoFocusField === 'gst') gstRef.current?.focus();
        else if (autoFocusField === 'website') websiteRef.current?.focus();
    }, [form_param]);
    const handleInputC = (e) => {
        setInputValue(e.target.value);
    };
    const handleInputG = (e) => {
        setGvalue(e.target.value);
    };
    const handleInputU = (e) => {
        setUrlvalue(e.target.value);
    };


    var sbmmtcls = "befstgo2 hovsub mdSub";
    var clsfrm = "nonImg";
    if (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') {
        clsfrm = "form-group-img";
        sbmmtcls = "submit-button-img";
    }

    return(<>
    <div>
        <div id="mdDiv">
        <Head_scr scr={"morereq"} hash ={form_param}  />
        {form_param.formType == 'BL' && <div className="eqs16" style={{ marginBottom: '20px' ,marginTop:'10px'}}>Enter your remaining contact details</div>}
        { !u_cname ?
            <div className={form_param.formType=='Enq' ? "eqflot beW5" : "cnamecont"}>
                <div className='lablcont'>
                <label htmlFor="companyName" style={{ fontSize: '14px' }}>Company/Business Name</label>
                <div className="tooltip">
                    <span className={`${(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') ? 'imagetool' : 'normaltool'} tooltiptext`}>
                        <span className='warhead'>Make sure your company name:</span>
                        <ul>
                            <li>is longer than 4 characters</li>
                            <li>is not the name of the contact person</li>
                            <li>is not the name of the product you sell</li>
                            <li>is not generic e.g. 'textile', 'SEO services', 'travel agency'</li>
                        </ul>
                    </span>
                </div>
                </div>
                <input 
                    id="companyName"
                    type="text"
                    value={cvalue} 
                    onChange={handleInputC} 
                    className={form_param.formType=='Enq' ? "ber-slbox inpt_errorbx inPlace mdplc newui" : 'cnamebl'}
                    placeholder="E.g., John Enterprises, Suguna Foods Private Limited"  
                    ref={companyRef}
                />
            </div> :""
        }
        { is_ga!=1 && visitoriso=='IN' ? 
            <div id="t0901_gst" className="eqflot beW5">
                <label htmlFor="gstask" style={{ fontSize: '14px',color:'#696969' }}>GST Number</label>
                <input id="t0901_gstname" type="text" name="_gstname" value={gvalue} onChange={handleInputG} className="ber-slbox inpt_errorbx inPlace mdplc newui" placeholder="Please enter GST Number to reach more sellers" maxlength="15" style={{ marginTop: '4px'}} ref ={gstRef}/>
            </div> : ""
        }    
        { is_ua!=1 && visitoriso!='IN' ? 
            <div id="t0901_gst" className="eqflot beW5">
                <label htmlFor="urlask" style={{ fontSize: '14px',color:'#696969' }}>Website URL</label>
                <input id="t0901_urlname" type="text" name="_urlname" value={urlvalue} onChange={handleInputU} className="ber-slbox inpt_errorbx inPlace mdplc newui" placeholder="Eg: www.johnenterprise.com" maxlength="15" style={{ marginTop: '4px'}} ref={websiteRef}/>
            </div> : ""
        }    
        </div>
        {nec_con_add && !state.newEnq && <Contactdtl fn={fn} em={em} mb={mb} ctid={ctid} iso={iso} phext={phext} form_param={form_param} gst={gvalue} cname={cvalue} wUrl={urlvalue} MoreReq={true} focusfield={focusfield}/>}
        {(!nec_con_add ||state.newEnq)  &&  <div className={clsfrm} ><input value="Submit" className={sbmmtcls} id="t0901_submit" type="submit" onClick={handleNextClick}></input></div>}</div>

    </>);
}
export default MoreReqDet;