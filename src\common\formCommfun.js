import SaveisqAPI from "../ISQ/SaveisqAPI";
export function isset(accessor) {
    try {
        // Note we're seeing if the returned value of our function is not
        // undefined or null
        return accessor() !== undefined && accessor() !== null
    } catch (e) {
        // And we're able to catch the Error it would normally throw for
        // referencing a property of undefined
        return false
    }
}
export function ISSET(value){
    return value != undefined && value != 'undefined' && value != null;
}
export function cookieDecodeREC(cookie){
    let decodestr = '';
    try{
        decodestr = safeDecodeURIComponent(cookie);
    }catch(e){
        try{
         decodestr = unescape(cookie);
        }
        catch(err){
            decodestr = cookie; 
        }
    }
    return decodestr;
} 

export function readCookieREC(name) {
    const cookies = document.cookie.split(';');

    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();

        // Check if the cookie name matches
        if (cookie.startsWith(name + '=')) {
            // Extract and return the cookie value
            return cookie.substring(name.length + 1);
        }
    }

    // <PERSON>ie not found
    return null;
}
export function loadimeshScriptREC(url) {
  var script = document.createElement("script");
  script.type = "text/javascript";
  script.src = url;
  // script.onload = callback;
  // script.onreadystatechange = function() {
  //   if (this.readyState == 'complete' || this.readyState == 'loaded') {
  //     callback();
  //   }
  // };

  document.head.appendChild(script);
}
export function imeqglval(){
  const imeqpresent = isset(() => readCookieREC('imEqGl')) ? readCookieREC('imEqGl') : '';
  const containsDispid = imeqpresent.includes('dispid');
  const containsBL = imeqpresent.includes('BL');
  let blenqarr = {"BL":false,"Enq":false ,"eqglval":imeqpresent};
  if (containsDispid && containsBL) {
      blenqarr["BL"]=true;
      blenqarr["Enq"]=true;
  } else if (containsDispid) {
      blenqarr["Enq"]=true;
  } else if (containsBL) {
      blenqarr["BL"]=true;
  }
  return blenqarr;
}
export function getparamValREC(cookieStr, key) {
    if (cookieStr !== "") {
        let decodedStr = cookieDecodeREC(cookieStr);
        let pattern = new RegExp(key + "=([^|&]*)");
        let match = decodedStr.match(pattern);
        if (match && match.length > 1) {
            return match[1];
        }
    }
    return "";
}

export function getcountryREC(){
    let imesh_data = '';
    let iploc_data = '';
    const imesh_cookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    const iploc_cookie = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';

    if (imesh_cookie != '') {
        imesh_data = getparamValREC(imesh_cookie, 'iso')? getparamValREC(imesh_cookie, 'iso') : window.userdata && window.userdata.iso &&  window.userdata.iso != 'FOR' ? window.userdata.iso : 'IN';
    }
    else {
        iploc_data = getparamValREC(iploc_cookie, 'gcniso');
    }
    let usr_cntry_iso = imesh_data != '' ? imesh_data : (iploc_data != '' ? iploc_data : 'IN');
    return usr_cntry_iso;
}
export function setCookieREC(name, value, days,isiploc) {
    let domain = document.domain == "localhost"? "localhost" :".indiamart.com";
    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  
    // Convert object to formatted string
    const cookieValue = isiploc == true ? value : Object.entries(value)
      .map(([key, val]) => `${key}=${encodeURIComponent(val)}`) // Encode each value
      .join('|'); // Join key-value pairs with '|'
  
    document.cookie = `${name}=${cookieValue};expires=${expires.toUTCString()};domain=${domain};path=/`;
  }
export function validateEmail(email) {
    // Define the regular expression for a valid email address
    const emailRegex = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;

    // Test the email against the regular expression
    return emailRegex.test(email);
}

  export function ReturnCorrectVal(val, defaultvalue) {
    if ( isset(() => val) && val !== "") {
      return val;
    } else {
      return isset(() => defaultvalue) ? (defaultvalue !== "" ? defaultvalue : "") : "";
    }
  }

  export function checkblockedUser(){
    const imesh_cookie = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
    let imesh_data='';
    if (imesh_cookie != '') {
        imesh_data = getparamValREC(imesh_cookie, 'usts') ? getparamValREC(imesh_cookie, 'usts') : window.userdata && window.userdata.usts? window.userdata.usts : '';
    }
    return imesh_data==2 ? true: true ;
  }
export function deleteCookieREC(name) {
	document.cookie = name + "=;expires=;domain=.indiamart.com;path=/;";
  }
export function getloginmode(){
  const imissCookie = isset(() => readCookieREC('im_iss')) && readCookieREC('im_iss');
  const imeshCookie = isset(() => readCookieREC('ImeshVisitor')) && readCookieREC('ImeshVisitor');
  return imissCookie && imeshCookie ? 1 : imeshCookie ? 2 : 3;
}

export function updateFulllogin(res){
  var imis = "";
  var keys = Object.keys(res.Response.LOGIN_DATA.im_iss);
  var keysLength = keys.length;
  
  keys.forEach(function(key, index) {
      imis += key + "=" + res.Response.LOGIN_DATA.im_iss[key];
      if (keysLength > 1 && index < keysLength - 1) {
          imis += "|";
      }
  });
  setCookieREC("im_iss", imis, 180,true);

}
export function stopBgScrollREC() {
  const html = document.documentElement;
  const wrapper = document.querySelector(".gl-wrapper");

  if (html.classList.contains("scrl_layout")) {
    html.classList.remove("scrl_layout");
  }

  if (wrapper) {
    wrapper.style.width = window.innerWidth + "px";
  }

  html.classList.add("scrl_layout");
}


export function resumeBgScrollREC() {
  const html = document.documentElement;
  const wrapper = document.querySelector(".gl-wrapper");

  if (html.classList.contains("scrl_layout")) {
    html.classList.remove("scrl_layout");
  }

  if (wrapper) {
    wrapper.style.width = "100%";
  }

  if (getComputedStyle(html).overflow === "hidden") {
    html.style.overflow = "auto";
  }
}



export function getAddressREC(LOCALITY, city, state) {
  let city_state = '';
  let address = '';

  if (city && (city.toLowerCase().match(/delhi/) || city.toLowerCase().match(/goa/))) {
    state = '';
  }
  if (state && (state.toLowerCase().match(/delhi/) || state.toLowerCase().match(/goa/))) {
    city = '';
  }

  if (isset(() => LOCALITY) && (LOCALITY != ''))
    city_state = LOCALITY;

  if (isset(() => city) && (city != '')) {
    if (isset(() => LOCALITY) && (LOCALITY != '')) {
      if (LOCALITY != city) { city_state += `, ${city}`; }
    } else {
      city_state += `${city}`;
    }
  }

  if (isset(() => state) && (state != '')) {
    if (isset(() => city) && (city != '') && (state != city))
      city_state += `, ${state}`;
    else if (!isset(() => city) && city == '' && isset(() => LOCALITY) && LOCALITY != '')
      city_state += `, ${state}`;
    else if (!isset(() => city) && city == '' && (!isset(() => LOCALITY)) && LOCALITY == '')
      city_state += `${state}`;
  }

  if (isset(() => city_state) && (city_state != '')) {
    address += city_state;
  }
  return address;
}

export function sessionValREC(keyName){
  let val = [];
  try {
    val= JSON.parse(sessionStorage.getItem(keyName)) || null;
  }  catch (e) {
    val = [];
  }

  return val;
}
export function ReturntoPropREC(form_param) {

  let returnValue = JSON.parse(JSON.stringify(form_param));

  returnValue.IsqArray = isset(() => form_param.IsqArray) ? form_param.IsqArray : [];
  returnValue.displayImage = isset(() => form_param.displayImage) ? form_param.displayImage : '';
  returnValue.prodName = isset(() => form_param.prodName) ? form_param.prodName : '';
  returnValue.generationId = isset(() => form_param.generationId) ? form_param.generationId : '';
  returnValue.mcatName = isset(() => form_param.mcatName) ? form_param.mcatName : '';

  return returnValue;
}

export function loadScriptREC(){
  if (window.IframeApiloaded === 0) {
     window.IframeApiloaded = 1;
     const tag = document.createElement('script');
     tag.src = 'https://www.youtube.com/player_api';
     const firstScriptTag = document.getElementsByTagName('script')[0];
     firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
  }
}


export function loadInstaScriptREC() {
const tag = document.createElement("script");
tag.src = "https://www.instagram.com/embed.js";
const firstScriptT = document.getElementsByTagName("script")[0];
firstScriptT.parentNode.insertBefore(tag, firstScriptT);
tag.onload = () => window.instgrm.Embeds.process();
}

export function reqFormGATrackREC(eventAction,form_param) {
    let event_type = "IMEvent";
    let el =  getEventLabelREC();
    let ec = "Send Enquiry - React" ;
    if(form_param.formType == "BL"){
      ec = "Post Buy Leads - React";
    }
    let cdMisc = MakeRefTextRecNew(form_param);
    imgtm.push( { 'event' : event_type, 'eventCategory' : ec, 'eventAction' : eventAction, 'eventLabel' : el, 'CD_Additional_Data' : cdMisc});
}

export function Eventtracking(ec,ea,form_param, ni){
  let event_type = "IMEvent";
  if(ni){
    event_type = "IMEvent-NI";
  }
  let evec = "Send Enquiry - React|" + ec;
  if(form_param.formType == "BL"){
    evec = "Post Buy Leads - React|" + ec;
  }
  let cdMisc = MakeRefTextRecNew(form_param);
  if(ec.includes("UserLogin") && shouldRenderPriceWidget(form_param)){
    cdMisc += "|PriceWidget";
  }
  imgtm.push( { 'event' : event_type, 'eventCategory' : evec, 'eventAction' : ea, 'eventLabel' : getEventLabelREC(), 'CD_Additional_Data' : cdMisc});
}
export function pageViewTrackREC(form_param, screenname){
  const imeshcookie = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor"): "";
  var isidentified = imeshcookie === "" ? "-new.mp" : ".mp";
  let el =  getEventLabelREC();
  let virtualPageViewURL = "/cgi/" + form_param.modId + "/Send Enquiry/"+el+isidentified;
  if(form_param.formType == "BL"){
    virtualPageViewURL = "/cgi/" + form_param.modId + "/Post Buy Leads/"+el+isidentified;
  }
  let cdAdditionalData = MakeRefTextRecNew(form_param);
  let virtualPageTitle = "Send Enquiry -FormOpen:DisplayStep|1|"+screenname;
  if(form_param.formType == "BL"){
    virtualPageTitle = "Post Buy Leads -FormOpen:DisplayStep|1|"+screenname;
  }
  imgtm.push({
    'event': "VirtualPageview",
    'virtualPageURL': virtualPageViewURL,
    'virtualPageTitle': virtualPageTitle,
    'CD_Additional_Data': cdAdditionalData
  });
}

    
export function getEventLabelREC() {
    const imeshcookie = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor"): "";
    let verify = getparamValREC(imeshcookie, "uv") ? getparamValREC(imeshcookie, 'uv') : window.userdata && window.userdata.uv? window.userdata.uv : '';
    var iso = window.countryiso || "IN";
    var isidentified =
      imeshcookie === ""
        ? "Unidentified"
        : verify === "V"
          ? "Verified"
          : "Unverified";
    var label = iso.toLowerCase() === "in" ? "Indian" : "Foreign";
    label += "|" + isidentified + "|" + labelNECREC(iso);
    return label;
}
export function labelNECREC(iso) {
    var nec = "";
    const imeshcookie = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor"): "";
    let name = getparamValREC(imeshcookie, "fn") ? getparamValREC(imeshcookie, 'fn') : window.userdata && window.userdata.fn? window.userdata.fn : '';
    let email = getparamValREC(imeshcookie, "em") ? getparamValREC(imeshcookie, 'em') : window.userdata && window.userdata.em? window.userdata.em : '';
    let ctid = getparamValREC(imeshcookie, "glid") && sessionStorage.getItem("minidtlsres") ? JSON.parse(sessionStorage.getItem("minidtlsres"))[getparamValREC(imeshcookie, "glid")] : "";
    let mobile = getparamValREC(imeshcookie, "mb1") ? getparamValREC(imeshcookie, 'mb1') : window.userdata && window.userdata.mb1? window.userdata.mb1 : '';
    if (name !="") nec += "N";
    if (iso === "IN") {
      if (email != "") nec += "E";
      if (ctid !="") nec += "C";
      if (nec === "") nec = "NO-NEC";
    } else {
      if (mobile!= "") nec += "M";
      if (nec === "") nec = "NO-NM";
    }
    return nec;
}

export function MakeRefTextRec(form_param) {

  const urlSearchParams = window.location && window.location.search ? new URLSearchParams(window.location.search) : '';
  let params = {};
  if (urlSearchParams) {
    urlSearchParams.forEach((value, key) => {
      params[key] = value;
    });
  }
  let site_entry = '';
  if (params && params.utm_campaign && params.utm_medium && params.utm_medium.includes("prd_ads")) {
    site_entry = `|utm_medium=${params.utm_medium}&&utm_campaign=${params.utm_campaign}`
  }

    let modIdf = form_param && form_param.modId ? form_param.modId : 'DIR';
    var ctan = ISSET(form_param.ctaName) ? form_param.ctaName : "";
    var ctaT=  ISSET(form_param.ctaType) ? form_param.ctaType : "";
  
    var ct_1 = ISSET(form_param.ct) ? (form_param.ct) : "" ;
    var mp_1 = ISSET(form_param.MP) ? (form_param.MP) : "" ;
    var ex_1 = ISSET(form_param.ex) ? (form_param.ex) : "" ;
    var img_f = ISSET(form_param.imgf) ? (form_param.imgf) : "" ;
    var isq_f = ISSET(form_param.isqf) ? (form_param.isqf) : "" ;
    var cm_1 = ISSET(form_param.cmconv) ? (form_param.cmconv) : "" ;
    var isDist = ISSET(form_param.isDist) ? (form_param.isDist) : "" ;
  
  
  
    if(ISSET(form_param.shownMultiImg) && form_param.shownMultiImg==1 && ISSET(form_param.ctaName)){
      if(form_param.ctaName.toLowerCase()==="image"){
        ctan="Image-M";
      }  
      else if(form_param.ctaName.toLowerCase()==="image_next"){
        ctan="Image-M_Next";
      }
      else if(form_param.ctaName.toLowerCase()==="image_pre"){
        ctan="Image-M_Pre";
      }
    }  
    // if(ctan.startsWith("Image") && imageVidNew(tmpId) && dirImpcatAB(tmpId)) {
    //   let suffix = isGAidEven() ? "-even" : "-odd";
    //   ctan = ctan.replace("Image", "Image" + suffix);
    //  }   
    form_param.refText =
      "ctaName=" + ctan;  
    form_param.refText +=
      "|ctaType=" + ctaT;
    form_param.refText +=
      "|PT=" +
      (ISSET(form_param.pageType) ? form_param.pageType : "");
  
    if(modIdf == "DIR" && ISSET(ct_1) &&  ISSET((form_param.pageType)) && (form_param.pageType).includes("city-mcat")){
        form_param.refText +=
        "|" +
        ct_1;
    }  
    if( ISSET(mp_1) &&  mp_1 != ""){
      form_param.refText +=
      "|" +
      mp_1;
    }
    if( ISSET(ex_1) &&  ex_1 != ""){
      form_param.refText +=
      "|ex=" +
      ex_1;
    }
    if( ISSET(cm_1) &&  cm_1 != ""){
      form_param.refText +=
      "|" +
      cm_1;
    }
    if( ISSET(img_f) &&  img_f != ""){
      form_param.refText +=
      "|" +
      img_f;
    }
    if( ISSET(isq_f) &&  isq_f != ""){
      form_param.refText +=
      "|" +
      isq_f;
    }
  
    form_param.refText +=
      "|Section=" +
      (ISSET(form_param.section) ? form_param.section : "");
    form_param.refText +=
      "|Position=" +
      (ISSET(form_param.position) ? form_param.position : "");
    form_param.refText +=
      "|ScriptVer=" +
      (ISSET(form_param.scriptVersion)
        ? form_param.scriptVersion
        : "");
        form_param.refText +=
        "|compRank=" +
        ((ISSET(form_param.compRank) && modIdf == "DIR") ? form_param.compRank : (ISSET(form_param.FCPRank) ? form_param.FCPRank : ""));
    form_param.refText +=
      "|searchTerm=" +
      (ISSET(form_param.mcatName) ? form_param.mcatName : "");
    if(ISSET(form_param.isCityID) && form_param.isCityID !== ""){
      form_param.refText += "|is_dist=" + isDist;
    } 
    if(ISSET(form_param.appendQRef) && form_param.appendQRef !== ""){
      form_param.refText += "|" + form_param.appendQRef;
    } 
    let sllr_rating = (ISSET(form_param.sllrRtng) && form_param.sllrRtng != "") ? form_param.sllrRtng : 0;
    if(modIdf == "DIR"){
      form_param.refText +=
      "|slr=" +
      sllr_rating;
    } 
    let MakeRefTextRc = form_param.refText || "";
    MakeRefTextRc += "|-React";
    MakeRefTextRc += LoginNewUiPDP(form_param) ? "-ABlogin" : "";
    MakeRefTextRc += imageabtest(form_param) ? "-FBimage" : "";
    MakeRefTextRc += window.widgetformed ? "|PriceWidget" : "";
    MakeRefTextRc += site_entry;
    return MakeRefTextRc;
  }
export function MakeRefTextRecNew(form_param) {
    let modIdf= form_param && form_param.modId ? form_param.modId : 'DIR';
    var ctan = ISSET(form_param.ctaName) ? form_param.ctaName : "";
    if(ISSET(form_param.shownMultiImg) && form_param.shownMultiImg==1 && ISSET(form_param.ctaName)){
      if(form_param.ctaName.toLowerCase()==="image"){
        ctan="Image-M";
      }  
      else if(form_param.ctaName.toLowerCase()==="image_next"){
        ctan="Image-M_Next";
      }
      else if(form_param.ctaName.toLowerCase()==="image_pre"){
        ctan="Image-M_Pre";
      }
    }
    // if(ctan.startsWith("Image") && imageVidNew(tmpId) && dirImpcatAB(tmpId)) {
    //   let suffix = isGAidEven() ? "-even" : "-odd";
    //   ctan = ctan.replace("Image", "Image" + suffix);
    //  }   
    
    var ctat = ISSET(form_param.ctaType) ? form_param.ctaType : "";
    ctan = ctan.replace(/\s/g, "");
    ctan = ctan.replace(/_/g, "");
    ctat = ctat.toLowerCase() === "product enquiry" ? "Prod" : ctat;
    var scriptv = ISSET(form_param.scriptVersion) ? form_param.scriptVersion : "";
    var symbol = "=";
    var index = scriptv.indexOf(symbol);
    var valueAfterSymbol = scriptv.substring(index + 1).trim();
    // if(isInactiveBL(tmpId) && inactiveScrollable()){
    //   ctan += isGAidEven() ? "-even" : "-odd";
    // }
    var ct_1 = ISSET(form_param.ct) ? (form_param.ct) : "" ;
    var mp_1 = ISSET(form_param.MP) ? (form_param.MP) : "" ;
  
  
    form_param.refText2 =
      "ctaN=" +  ctan;
    form_param.refText2 +=
      "|ctaT=" +  ctat;
    form_param.refText2 +=
      "|PT=" +
      (ISSET(form_param.pageType) ? form_param.pageType : "");
  
    if(modIdf == "DIR" && ISSET(ct_1) &&  ISSET((form_param.pageType)) && (form_param.pageType).includes("city-mcat")){
        form_param.refText2 +=
        "|" +
        ct_1;
        
    }
    if(ISSET(mp_1) &&  mp_1 != ""){
      form_param.refText2 +=
      "|" +
      mp_1;
  
    }
      
    form_param.refText2 +=
      "|Sec=" +
      (ISSET(form_param.section) ? form_param.section : "");
    form_param.refText2 +=
      "|Pos=" +
      (ISSET(form_param.position) ? form_param.position : "");
    form_param.refText2 +=
      "|v=" +  valueAfterSymbol;
    let MakeRefTextRecNew = form_param.refText2 + "|React" || "";
    MakeRefTextRecNew += LoginNewUiPDP(form_param) ? "-ABlogin" : "";
    MakeRefTextRecNew += window.widgetformed ? "|PriceWidget" : "";
    MakeRefTextRecNew += imageabtest(form_param) ? "-FBimage" : "";
    return MakeRefTextRecNew;
}

export function scriptTag(s) {
  if(isset(()=>s)){
    let name = s.toLowerCase();
    if (
      name.includes("<script>") ||
      name.includes("</script>") ||
      name.includes("script&gt") ||
      name.includes("%3cscript") ||
      name.includes("script%253e") ||
      (name.includes("<") && name.includes(">"))
    )
      return true;
  } 
  return false;
}

export function isInactBL(form_param){
  if(isset(()=>form_param) && isset(()=>form_param.ctaName)){

    if(form_param.ctaName.toLowerCase()=='inactive-bl' || form_param.ctaName.toLowerCase()=='inactive'){
      return true;
    }


  }  
  return false;

}

export function isInlineBl(form_param){
  if(isset(()=>form_param) && isset(()=>form_param.tempId)){
    if(form_param.tempId =="01"){
      return true;
    }
  }  
  return false;

}

export function curUrlAppend(form_param){

    let url = window.location.href;
  
    if (isset(()=>form_param.pdpTemplate) && form_param.pdpTemplate !== "") {
      if (isset(()=>url) && url !== "") {
        url = url.endsWith(".html") ? `${url}?tempid=${form_param.pdpTemplate}` : `${url}&tempid=${form_param.pdpTemplate}`;
      }
    }
  
    return url;
  
 

}


export function currentISO() {
  const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
  const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
  var iso = getparamValREC(imesh, 'iso') !== "" ? getparamValREC(imesh, 'iso') : window.countryiso ? window.countryiso : window.userdata && window.userdata.iso &&  window.userdata.iso != 'FOR' ? window.userdata.iso: getparamValREC(iploc, 'gcniso') !== "" ? getparamValREC(iploc, 'gcniso') : "IN";
  return iso;
};

export function callAdsGpt(){
  if(isset(()=>window.isadsloaded) && window.isadsloaded == 0){
      var script = document.createElement('script');
      script.setAttribute('async', 'async');
      script.src = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';
      document.getElementsByTagName("head")[0].appendChild(script);
      window.isadsloaded = 1;
  }
}

export function loadScriptGtag(src, callback, async = true, defer = true) {
    var s = document.createElement('script');
    s.setAttribute('src', src);
    callback ? s.onload = callback : '';
    async ? s.async = true : '';
    defer ? s.defer = true : '';
    document.body.appendChild(s);
    // console.log('called')
}

export function getActivityIdREC(form_param) {
  let modIdf = form_param && form_param.modId ? form_param.modId : 'DIR';
  var activity_id = "";
  var dirtype = "";
  var ctaName = ISSET(form_param.ctaName) ? form_param.ctaName.toLowerCase() : "";
  var section = ISSET(form_param.section) ? form_param.section.toLowerCase() : "";
  var url = window.location.href;
  if ( typeof ims !== "undefined" && ISSET(ims) && ISSET(ims.dirTypeFull) && ims.dirTypeFull !== "" )
    dirtype = ims.dirTypeFull.toLowerCase();
  if (modIdf === "DIR") {
    if (dirtype === "impcat" || dirtype === "city") {
      if (ctaName === "view mob e"  || ctaName === "click to call") {
        activity_id = "4242";
      } else if (ctaName === "contact seller") {
        activity_id = "4243";
        if (section === "listing-similarproducts") {
          activity_id = "4251";
        }
      } else if (ctaName === "get latest price") {
        activity_id = "4244";
      } else if (ctaName === "ask price") {
        activity_id = "4245";
      } else if (ctaName === "no price") {
        activity_id = "4246";
      } else if ((ctaName === "image") || (ctaName === "product video")) {
        activity_id = "4249";
        if (section === "listing-similarproducts") {
          activity_id = "4249";
        }
      } else if (ctaName === "company video") {
        activity_id = "4248";
      } else if (ctaName === "contact_supplier") {
        activity_id = "4251";
      }
    } else if (dirtype === "search" ) {
      if (ctaName === "view mob e"  || ctaName === "click to call") {
        activity_id = "4252";
      } else if (ctaName === "contact seller") {
        activity_id = "4253";
      } else if (ctaName === "get latest price") {
        activity_id = "4254";
      } else if (ctaName === "ask price" || ctaName === "no price") {
        activity_id = "4255";
      } else if (ctaName === "get quotes") {
        activity_id = "4256";
      } else if ((ctaName === "image") || (ctaName === "product video")) {
        activity_id = "4257";
      } else if (ctaName === "company video") {
        activity_id = "4258";
      }
    }
  }
else if(modIdf === "PRODDTL"){
  if (ctaName === "get latest price" || ctaName === "contact seller") {
    activity_id = "4269";
  } else if (ctaName === "view mob e" || ctaName === "click to call") {
    activity_id = "4270";
  } else if (ctaName === "image") {
    activity_id = "4271";
  } else if (ctaName === "video") {
    activity_id = "4272";
  } else if (ctaName === "ask price") {
    activity_id = "4273";
  }
} 
  return activity_id;
}

export function cslModidR(modid) {
  const validModids = ['MY', 'SELLERS', 'DIR', 'ETO', 'TDR'];

  if (validModids.includes(modid)) {
    if (modid == 'SELLERS') {
      return 'SELLERMY'
    }
    return modid;
  } else {
    return 'IM';
  }
}

export function getActivityTimeREC() {
  var now = new Date();

  var year = now.getFullYear();
  var month = (now.getMonth() + 1 < 10 ? '0' : '') + (now.getMonth() + 1);
  var day = (now.getDate() < 10 ? '0' : '') + now.getDate();
  var hours = (now.getHours() < 10 ? '0' : '') + now.getHours();
  var minutes = (now.getMinutes() < 10 ? '0' : '') + now.getMinutes();
  var seconds = (now.getSeconds() < 10 ? '0' : '') + now.getSeconds();

  var formattedDateTime = year + month + day + hours + minutes + seconds;
  return formattedDateTime;
}

export function recordOutboundd(event, category, action, value,link, nonInteraction=0)
{
    // if(event!=undefined && event==0)
    //     event = 'IMEvent';
    // else if(event!=undefined && event==1)
    //     event = 'IMEvent-NI';
    // else
    //     event = 'IMEvent';

        event = 'IMEvent-NI';

    var linkHref='';
    if(typeof(link)=='string' && link.match(/http:/))
    {
        linkHref = link;
    }
    else
    {
        linkHref = link.href;
    }
    imgtm.push({ 'event' : event,'eventCategory' : category,'eventAction' : action,'eventLabel' : link,'eventValue': value, non_interaction: nonInteraction,'CD_Additional_Data' : ''});

   
    
   
}

export function ErrorsLOG(message, source, lineno, colno, error) {
  let misc_details=" | Line no : "+lineno+" | col no : "+colno+" | Error : "+error;
  // console.log('e  :', message)
  recordOutboundd(0,'Forms-Error', message+misc_details, 0, source);   
}

export function SaveISQonCross(form_param, selectedOptions, postreq) {
  if(selectedOptions.length>0){
      const b_response = selectedOptions.map(opt => opt.b_response);
      const q_desc = selectedOptions.map(opt => opt.q_desc);
      const q_id = selectedOptions.map(opt => opt.q_id);
      const b_id = selectedOptions.map(opt => opt.b_id);      
      SaveisqAPI(form_param, postreq, b_response, q_desc, q_id,b_id);
    }              
}

export function isABTest(form_param){
  if((isset(()=>form_param.newABtest) && form_param.newABtest==1)){
    return true;
  }
  return false;
}
export function isPDfForm(form_param){
  if(form_param.ctaType == 'pdf' && isset(() => form_param.pdfurl) && form_param.pdfurl != ''){
    return true;
  }
  return false;
}

export function checkNetworkConnection(){
  if ('connection' in navigator) {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;  
    if(isset(()=>connection) && isset(()=>connection.effectiveType))    
      return connection.effectiveType;      
  } 
  else{
      return '2g';
  }
}


export  function loadAdSenseScript(){
  if (!document.querySelector("script[src='https://www.google.com/adsense/search/ads.js']")) {
      const adsScript = document.createElement("script");
      adsScript.src = "https://www.google.com/adsense/search/ads.js";
      adsScript.async = true;
      // adsScript.onload = () => loadAds(); // Call loadAds after script loads
      document.head.appendChild(adsScript);

      // Initialization script after ads.js loads
      const initScript = document.createElement("script");
      initScript.type = "text/javascript";
      initScript.charset = "utf-8";
      initScript.innerHTML = `
          (function(g,o){
              g[o]=g[o]||function(){
                  (g[o]['q']=g[o]['q']||[]).push(arguments)
              },
              g[o]['t']=1*new Date
          })(window,'_googCsa');
      `;
      document.head.appendChild(initScript);
  } 

}
export function updateimeshCombined(cookie, useArrayLogic) {
  const getValue = (index, key) => {
    if (useArrayLogic) {
      return cookie[index] === "1" ? cookie[index] : "";
    }
    return getUserData(cookie, key) !== "" ? "1" : "";
  };
const imeshObj = {
    fn: getValue(0, "fn"),
    ln: getValue(1, "ln"),
    em: getValue(2, "em"),
    phcc: useArrayLogic 
      ? (cookie[3] === "2" ? "" : (cookie[3] === "1" ? "91" : cookie[3])) 
      : (getUserData(cookie, "phcc") !== "" ? (getUserData(cookie, "phcc") === "91" ? "91" : "0") : ""),
    iso: useArrayLogic 
      ? (cookie[4] === "2" ? getparamValREC(readCookieREC('iploc'), 'gcniso') ||  "IN" : (cookie[4] === "1" ? "IN" : "FOR")) 
      : (getUserData(cookie, "iso") !== "" ? (getUserData(cookie, "iso") === "IN" ? "IN" : "FOR") : ""),
    mb1: getValue(5, "mb1"),
    ctid: getValue(6, "ctid"),
    glid: getparamValREC(readCookieREC('ImeshVisitor'), "glid") || '',
    utyp: useArrayLogic 
      ? (cookie[8] === "1" ? "N" : cookie[8] === "2" ? "F" : cookie[8] === "3" ? "P" : "") 
      : getUserData(cookie, "utyp"),
    ev: useArrayLogic ? (cookie[9] === "1" ? "V" : "") : getUserData(cookie, "ev"),
    uv: useArrayLogic ? (cookie[10] === "1" ? "V" : "") : getUserData(cookie, "uv"),
    usts: useArrayLogic ? (cookie[11] === "2" ? "2" : "") : getUserData(cookie, "usts")
  };
  return imeshObj;
}
function getUserData(imeshcookie, key) {
  const cookieValue = getparamValREC(imeshcookie, key);
  return cookieValue && cookieValue.trim() !== "" ? cookieValue : window.userdata ? window.userdata[key] : "";
}
export function IsCookieHaveValue(){
  const imeshcookie = readCookieREC("ImeshVisitor");
  if(imeshcookie && (getparamValREC(imeshcookie, "mb1") =='') && (getparamValREC(imeshcookie, "em") =='') ){
    return false; 
  }
  return true;
}

export function newTYform(){
  return false;
}

// export function isGAendingwith2() {
//   const imeshcookie = isset(() => readCookieREC("ImeshVisitor")) ? readCookieREC("ImeshVisitor"): "";

//   if (imeshcookie) {
//     let glusrid  = getparamValREC(imeshcookie, 'glid');
//     if (glusrid && glusrid % 10 == 2) {
//       return true;
//     } else {
//       return false;
//     }

//   }
//   else {
//     return false;
//   }

// }

export function isPresent(val){
 if(isset(() => val) && val && val!="0"){
  return true;
 }
 return false;
}

export function safeDecodeURIComponent(str){
  try {
    return decodeURIComponent(str);
  } catch {
    return str; // Return the original string if decoding fails
  }
};

export const formatINRPrices = (price='') => {
  const prceStr = price.toString();
  let PriceDec = prceStr && prceStr.split('.') ? prceStr.split('.') : ''
  let priceStr = PriceDec && PriceDec[0] ? PriceDec[0] : '';
  let formattedPrice = '';
  let i = priceStr.length - 1;
  let count = 0;
  let flag=1
  while (i >= 0) {
    if ((flag==1 && count === 3) || (flag==0 && count===2)) {
      formattedPrice = ',' + formattedPrice;
      flag=0;
      count = 0;
    }
    formattedPrice = priceStr[i] + formattedPrice;
    i--;
    count++;
  }
 return formattedPrice + (PriceDec[1]?`.${PriceDec[1]}`:"")
}

export function smartcitySug(desc) {
  return ["pickup city", "drop city", "pickup location", "drop location","pickup city / location","drop city / location"].includes(desc.toLowerCase());
}

export function searchAllindia(form_param) {
  const url = window.location.href;
  return (
    form_param?.modId === 'DIR' &&
    !url.includes('cq:') &&
    !url.includes('cq=')
  );
}
export function LoginNewUiPDP(form_param) {
  var gacookie = readCookieREC('_ga') && readCookieREC('_ga').slice(-1) || '';
  var isEvenDigit = !isNaN(gacookie) && Number(gacookie) % 2 == 0;
  if (form_param.modId == "PRODDTL" && form_param.formType == "Enq" && form_param.tempId == "09" && (form_param.ctaType != "Image" && form_param.ctaType != "Video" && form_param.ctaType != 'pdf') && window.countryiso == "IN") {
    return true;
  }
  return false;
}
export function imageabtest(form_param){
  // var gacookie = readCookieREC('_ga') && readCookieREC('_ga').slice(-1) || '';
  // var gacookieending = gacookie && gacookie == '3' ? true : false;
  if(form_param.ctaType == "Image" || form_param.ctaType == "Video"){
    return true;
  }
  return false;
}

export function callbacktoProp(form_param){
  if (typeof form_param.enqSentCallBack === "function") {
    form_param.enqSentCallBack(form_param);
  }
  else{
    return;
  }
}

export function wrapWithRAF(calledFunc) {
  return (...args) => {
    requestAnimationFrame(() => {
      setTimeout(() => {
        calledFunc(...args);
      }, 0);
    });
  };
}

export function isBlRevampd(form_param) {
  if(form_param.modId == "PRODDTL" && form_param.formType == 'BL' && !isInactBL(form_param)){
    return true;
  }
  return false;
}

export function srchQuryHandling(route) {
  route=route.replace(/%20/g, '+')
  return route;
}

// Function to check if Price Widget should be rendered
export function shouldRenderPriceWidget(form_param) {
  // Check if form_param exists
  if (!form_param) {
    return false;
  }
  if(window.LoginmodeReact != '3'){
    return false;
  }

  // Check if modId is 'proddtl'
  const modId = form_param.modId || form_param.modid || '';
  if (modId.toLowerCase() !== 'proddtl') {
    return false;
  }

  // Check if ctaName is 'get latest price'
  const ctaName = form_param.ctaName || '';
  if (ctaName.toLowerCase() !== 'get latest price') {
    return false;
  }

  // Check if price_flag is 2
  const priceFlag = form_param.price_flag;
  if (priceFlag !== 2) {
    return false;
  }

  // Check if imageFlag is 4
  const imageFlag = form_param.imageFlag;
  if (imageFlag !== 4) {
    return false;
  }

  // Check if pDispId ends with 3
  const pDispId = form_param.pDispId || '';
  if (!pDispId.toString().endsWith('3')) {
    return false;
  }

  // All conditions met - render the widget
  return true;
}