import { isset,readCookieREC,getparamValREC,currentISO } from "./formCommfun"
const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
      const iploc_iso = getparamValREC(iploc, 'gcniso') !== "" ? getparamValREC(iploc, 'gcniso') : "IN";
export const defState= {
    frscr: 2,
    // inlinRD: true,
    searchshown: false,
    prevtrack: "",
    currentscreen: "",
    Isqform: false,
    IsqformBL: false,
    OTPcon: false,
    NECcon: false,
    Imeshform: true,
    multiImg: false,
    RDform : false,
    RDformBL : false,
    MrEnrForm  : false,
    postreq :0,
    thankyou: false,
    PrevNext: 0,
    singleimgsld: false,
    // Inlnbl: false,
    // inlineShownfields: true,
    newEnq : false,
    progressCnt : currentISO() || iploc_iso,
    progBar : false,
    RDNECcon :false,
    UserData : {fn: "", ln: "", em: "", phcc: "", iso: "", mb1: "", ctid: "", glid: "", utyp: "", ev: "", uv: "", usts: ""},
    progressstep :1,
    priceWidgetApiResponse: false,
    isqScreenReached: false,
}
