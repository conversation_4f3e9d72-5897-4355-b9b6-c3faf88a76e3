.errorOtpR {
    color: #f40c10;
    height: auto;
    line-height: 26px;
    /* position: absolute; */
    margin-top: 10px;
}
.errorOtpR2 {
    margin-top: 5px;
    color: #f40c10;
    height: auto;
    line-height: 26px;
}
.bedvh {
    min-height: 38px;
  }
  .oEq_r .enqoptin-rec {
    width: 38px;
    height: 38px;
    border-radius: 3px;
    border: solid 1px #777777;
    background-color: #ffffff;
    font-size: 18px;
}
.enqoptin-rec {
    border: 0px;
    border-bottom: 2px solid #b9b9b9;
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 16px;
    color: #000;
    margin-right: 15px;
    padding: 0 5px;
    text-align: center;
    font-size: 20px;
}

.bloptin-rec {
    border: 0;
    border-bottom: 2px solid #b9b9b9;
    width: 38px;
    height: 38px;
    line-height: 28px;
    color: #000;
    margin-right: 15px;
    padding: 0 5px;
    font-size: 20px;
}

.nootpp{
    margin-top: 95px;
    text-align: left;
    min-width: 250px;
    color: #606060;
}
.nootppbl{
    margin-top: 95px;
    min-width: 250px;
    color: #606060;
}
#noOtp #t0901resendOtp {
    padding: 1px 6px !important;
}

.blSmst {
    color: #2e3192;
    text-decoration: underline;
    border: 0px;
    background: transparent;
    font-size: 14px;
}

.oEq_r .blotp {
    color: #111; 
     font-size: 16px; 
    margin-top: 15px;
    line-height: 21px;
}
.oEq_r .hovsub:hover{
    background-color: #007a6e !important;
    border-color: #007a6e !important;
}
.bemb20{
    margin-bottom: 20px;
}
.bloptin-rec:focus{
    border-top:none!important;
    border-right:none!important;
    border-left:none!important;
    border-bottom: 2px solid #00a699!important;}
.enqoptin-rec:focus {
    border-color: #029f93!important;
}

.bloptin-rec:focus-visible,.enqoptin-rec:focus-visible  {
    outline: none; 
  }

  #t0901resendOtp:hover{
    text-decoration: none;
  }
  .skipOTP{
    background-color: transparent;
    border: none;
    color: #999;
    cursor:pointer; 
  }
  .skipOTP:hover{
    color: black;
  }
  .skipOTPFirst{
    background-color: transparent;
    border: none;
    color: #999;
    cursor:pointer; 
    padding: 0px;
  }
  .skipOTPFirst:hover{
    color: black;
  }

  .disabRes{
    
        opacity: 0.4;
                cursor: default;
    
  }
  .blotpbtn {
    background: #00a699;
    padding: 10px 20px;
    font-size: 16px;
    color: #fff;
    border: 0px;
    border-radius: 2px;
    font-family: arial;
    margin-left: 5px;
    outline: 0;
    cursor: pointer;
}
.blicotp {
    background-position: -135px -685px;
    width: 121px;
    height: 96px;
    display: inline-block;
}
.blnewfo_sprit{
    background-image: url(https://apps.imimg.com/gifs/blform-sprite22.png);
    background-repeat: no-repeat;
}
.beotpR{
    margin-top: 40px;
    text-align: center;
}
.centred{
    text-align: center;
}   
.getotpcls{
    background-color: rgb(0, 166, 153);
    width: 200px;
    height: 45px;
    border: solid 2px #029f93;
    font-size: 18px !important;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    color: #ffffff;
    cursor: pointer;
    margin: 20px 0px;
    border-radius: 8px;
}
.otpbtnfontimg{
    font-size: 14px;
}
.otpbtnfont{
    font-size: 20px;
}
.getotpcls:hover{
    background-color: #007a6e ;
    border-color: #007a6e;
}
.otpconfirm{
    color: #068076;
    font-size: 16px; 
    margin-top: 15px;
    line-height: 21px;
}
