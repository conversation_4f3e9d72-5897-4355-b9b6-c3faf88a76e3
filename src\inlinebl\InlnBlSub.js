import React, { useEffect } from "react";
import { useGlobalState } from '../context/store';
import { readCookieREC, getparamValREC, isset, updateimeshCombined, Eventtracking} from '../common/formCommfun';
import IntGenApi from '../main/IntGenApi';
import { validateEmailId, validatePhoneNumber } from "../Login/VldNmEm";
import LoginApiCall from "../Login/LoginApiCall";
import GlusrUpdate from "../Login/GlusrUpdate";
import mcatdtl from "../BL_Popup/McatDTLServ";
import SessionDetailApi from "../Login/SessionDeatilApi";
import getMcatDetail from "./McatNameSugg";
function InlnBlSub({form_param,seterr_msg,usrnm,isGDPRcheck,country_iso, GDPRiso ,setGDPRerrmsg ,mob_value,email_val, setusrnm , handleoutsideclick, setImeshExist,blsearchval}) {
    const { state, dispatch } = useGlobalState();
    async function afterlogin(loginres, fromlogin){
        const imesh = readCookieREC('ImeshVisitor');
        const imeshglid = getparamValREC(imesh, 'glid');

        if(country_iso != "IN" && !loginres.fn){
            const user_data = { glid: imeshglid, name: usrnm, s_email: email_val , country_iso: country_iso || "IN"}
            const glusrdata = await GlusrUpdate(form_param, user_data);
            dispatch({ type: 'UserData', payload: { UserData: glusrdata.DataCookie } });
        }
        if(!state.openBLPopup && form_param.displayImage =="" && form_param.mcatId != "" && form_param.mcatId != -1){
            const resp = await mcatdtl(form_param);
            dispatch({ type: 'openBLPopup', payload: { openBLPopup:  resp} });
        }
        if(fromlogin){
            IntGenApi(form_param, "loginscreen", country_iso);
            Eventtracking(`SS1-UserLogin`,state.prevtrack,form_param,false);
            dispatch({ type: 'prevtrack', payload: { prevtrack: `-UserLogin` } });
        }else{
            IntGenApi(form_param,"",country_iso);
            Eventtracking(`SS1-ProductName`,state.prevtrack,form_param,false);
            dispatch({ type: 'prevtrack', payload: { prevtrack: `-ProductName` } });
        } 
        window.screencount++;
        if(isset(()=>form_param.formState)){
            form_param.formState(true);
        }
        dispatch({ type: 'openinlnBLPopup', payload: { openinlnBLPopup: true } });
    }
    async function openblpopup(){
        if(blsearchval == ""){
            seterr_msg("Please enter your requirement");
            return;
        }else{
            if(form_param.prodName != blsearchval){
                await getMcatDetail(blsearchval,form_param);
                form_param.prodName = blsearchval;
                window.inlineparambl= form_param;
                const resp = await mcatdtl(form_param);
                dispatch({ type: 'openBLPopup', payload: { openBLPopup:  resp} });
            }
        }
        const ismshexist = readCookieREC("ImeshVisitor");
        if(!ismshexist){
            let valid_check = "mob";
            let fld_val = mob_value;
            if (country_iso == "IN") {
                valid_check = validatePhoneNumber(mob_value);
            }else{
                valid_check = validateEmailId(email_val);
                fld_val = email_val;
            }
            if(valid_check !=""){
                seterr_msg(valid_check);
                return;
            }
            if(country_iso != "IN" && usrnm == ""){
                const alreadyexist = await handleoutsideclick();
                if(!alreadyexist){
                    seterr_msg("Please enter you name");
                    return;
                }
            }
            if(GDPRiso && !isGDPRcheck){
                setGDPRerrmsg("Please Agree to the Terms and Conditions");
                return;
            }
            const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
            const s_ip = getparamValREC(iploc, 'gip');
            const loginres = await LoginApiCall(form_param, fld_val,"",s_ip,country_iso) ;
            if(loginres.code == '200'){
                dispatch({ type: 'UserData', payload: { UserData: loginres.DataCookie } });
                dispatch({ type: 'country_iso', payload: { country_iso: loginres.iso } });
                if (loginres.DataCookie && usrnm == "") {
                    setusrnm(loginres.DataCookie.fn);
                }
                setImeshExist(readCookieREC("ImeshVisitor"));
                dispatch({ type: "Imeshform", payload: { Imeshform: true } });
                afterlogin(loginres.DataCookie, true);
            }
        }
        if(ismshexist){
            let responseData = {};
            if(getparamValREC(ismshexist, 'sessionKey')!=''){
                responseData = await SessionDetailApi(getparamValREC(ismshexist, 'sessionKey'),form_param.modId);
                if(responseData && responseData.DataCookie){
                dispatch({ type: 'UserData', payload: { UserData: responseData.DataCookie } });}
                dispatch({ type: 'country_iso', payload: { country_iso: responseData.iso } });
            }
            else{
                responseData.DataCookie = updateimeshCombined(ismshexist,false);
                dispatch({ type: 'UserData', payload: { UserData: responseData.DataCookie } });
            }
            afterlogin(responseData.DataCookie, false);
        }
    }
    useEffect(() => {
        window.userdata = state.UserData;
    }, [state.UserData]);
    return (
        <>
            <div className="idsf jcc"><input type="submit" id="t0102_submit" className="inlSbtn crP hovsub" value="Submit Requirement" onClick={openblpopup}/></div>
        </>
    )
}
export default InlnBlSub;