/* Price Widget Styles */
.price-widget {
  /* border: 1px solid #dee2e6;
  border-radius: 8px; */
  margin-bottom: 12px;
  position: relative;
  overflow: visible;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  transition: all 0.3s ease;
  max-width: 540px;
  width: 320px;
  height: auto;
}

.price-widget-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.price-widget-header {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  color: #0d9488;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 20px;
  position: relative;
  z-index: 10;
}

.price-widget-content {
  background: #f0fdfa;
  padding: 12px 16px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  opacity: 0;
  transform: translateY(0) scale(1);
  will-change: opacity;
  animation: fadeInContent 1s ease-out forwards;
}

@keyframes fadeInContent {
  0% {
    opacity: 0;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.price-widget-content.blurred {
  filter: blur(3px);
  pointer-events: none;
  position: relative;
}

/* Lowest Price Product Display */
.lowest-price-product {
  display: flex;
  gap: 100px;
  /* padding: 8px 12px; */
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.product-price-container {
  flex-shrink: 0;
  width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.current-price-label {
  font-size: 10px;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.main-product-price {
  text-align: center;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
}

.price-value-red {
  font-size: 18px;
  font-weight: 700;
  color: #dc3545;
}

.price-unit-grey {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}



.product-details {
  /* flex: 1; */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
}

.product-name {
  font-size: 10px;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  margin-bottom: 4px;
}



.product-price {
  text-align: center;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 2px;
}

.product-price .price-value {
  font-size: 18px;
  font-weight: 700;
  color: #1b5e20;
}

.product-price .price-unit {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.price-comparison {
  font-size: 14px;
  font-weight: 600;
  color: #388e3c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.percentage-less {
  background: #e8f5e9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.down-arrow {
  font-size: 10px;
  color: #388e3c;
}

/* Price Comparison Bar */
.price-comparison-bar {
  margin-top: 4px;
  /* padding: 12px 16px; */
}

.price-bar-container {
  position: relative;
  height: 8px;
  width: 425px;
  background: linear-gradient(to right, #c70014 0%, #ffbf01 50%, rgb(0, 106, 25) 100%);
  border-radius: 4px;
  margin: 8px auto;
}



.price-markers {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: #6c757d;
  width: 500px;
}

.price-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.price-marker-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  position: absolute;
  top: -3px;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.price-marker-dot:hover {
  transform: translateX(-50%) scale(1.2);
}

/* Tooltip styles */
.price-tooltip {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  z-index: 10;
  pointer-events: none;
}

.price-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.price-marker-dot:hover .price-tooltip {
  opacity: 1;
  visibility: visible;
}

.current-product-dot {
  background: #c70014;
  border-color: #fff;
}

.lowest-product-dot {
  background: rgb(0, 106, 25);
  border-color: #fff;
}

.average-product-dot {
  background: #ffbf01;
  border-color: #fff;
}

.price-marker-label {
  font-weight: 600;
  margin-top: 8px;
  text-align: center;
  line-height: 1.2;
  font-size: 12px;
}

.current-product-label {
  color: #dc3545;
}

.lowest-product-label {
  color: #28a745;
}

.average-product-label {
  color: #ffc107;
}



/* Negotiation Tip */
.negotiation-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #c3e6c3;
  border-radius: 6px;
  font-size: 11px;
  color: #2d5a2d;
}

.tip-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tip-text {
  font-weight: 500;
  line-height: 1.3;
}



.market-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-stable {
  color: #28a745;
}

.trend-rising {
  color: #dc3545;
}

.trend-falling {
  color: #ffc107;
}

.confidence-score {
  font-weight: 500;
}

/* Login Prompt Styles - No overlay needed, direct content */

.login-prompt {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  gap: 8px;
  justify-content: center;
}

.locked-icon {
  font-size: 16px;
  color: #ff6b35;
  background: #ffedd5;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
svg:not(:host).svg-inline--fa, svg:not(:root).svg-inline--fa {
    overflow: visible;
    box-sizing: content-box;
}
.svg-inline--fa {
    display: var(--fa-display, inline-block);
    height: 1em;
    overflow: visible;
    vertical-align: -.125em;
}

.prompt-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.main-message {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
  margin: 0;
}

.disclaimer {
  font-size: 12px;
  color: #4b5563;
  line-height: 1.2;
  font-weight: 400;
  font-style: italic;
  margin: 0;
}

/* Loading State with Shining Text */
.price-widget-loading {
  background: #f0fdfa;
  padding: 12px 16px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  z-index: 2;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  will-change: opacity;
  animation: fadeOutLoading 0.8s ease-in-out forwards;
  animation-delay: 0.4s;
}

@keyframes fadeOutLoading {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(0) scale(1);
    visibility: hidden;
  }
}

.loading-lock {
  width: 32px;
  height: 32px;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
}

.loading-lock::before {
  content: '🔒';
  font-size: 16px;
}

@keyframes textShine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.loading-text {
  font-size: 12px;
  margin: 0;
  font-weight: 600;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 40%);
  white-space: nowrap;
  background: linear-gradient(
    90deg,
    #6c757d 0%,
    #ffffff 50%,
    #6c757d 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShine 2s ease-in-out infinite;
}

/* Error State */
.price-widget-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #dc3545;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .price-widget {
    margin-bottom: 10px;
    max-width: 400px;
    width: 300px;
  }

  .price-widget-header {
    padding: 10px 14px;
    font-size: 17px;
  }

  .price-widget-container {
    height: 100px;
  }

  .price-widget-content {
    padding: 10px 14px;
    height: 100px;
  }

  .price-widget-loading {
    padding: 10px 14px;
    height: 100px;
  }

  .lowest-price-product {
    gap: 50px;
    /* padding: 6px 10px; */
  }

  .current-price-label {
    font-size: 8px;
  }

  .price-value-red {
    font-size: 16px;
  }

  .price-unit-grey {
    font-size: 12px;
  }

  .product-name {
    font-size: 8px;
  }

  .product-price .price-value {
    font-size: 16px;
  }

  .product-price .price-unit {
    font-size: 12px;
  }

  .locked-icon {
    font-size: 14px;
    width: 24px;
    height: 24px;
  }



  .negotiation-tip {
    margin-top: 8px;
    padding: 6px 10px;
    font-size: 10px;
    gap: 6px;
  }

  .tip-icon {
    font-size: 12px;
  }

  .main-message {
    font-size: 15px;
  }

  .disclaimer {
    font-size: 11px;
  }

  .login-overlay {
    padding: 8px;
  }
}

/* Integration with existing form styles */
.ber-frmpop .price-widget {
  margin: 0 0 16px 0;
}

.ber-Rsc .price-widget {
  width: 100%;
  box-sizing: border-box;
}
