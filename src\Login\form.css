.scrl_layout {
    overflow: hidden !important;
}
.ber-frwrap {
    z-index: 999;
}
.w100{
    width: 100%; 
}
.impw100{
    width: 100% !important;  
}
.highErr{
    border-color: #d6181b !important;
}
.quantiyerr{
    color:#d6181b !important;
    padding: 0px 0px 5px 0px;
}
.errorRD{
    color: red;
    margin-top: 8px;
}
.lhSldby{
    line-height: 25px;
}
.blckbg {
    background: #000;
    overflow: hidden;
    filter: alpha(opacity = 75);
    opacity: 0.75;
}
.blckbg, .ber-frwrap {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0px;
    top: 0px;
    bottom: 0;
    text-align: left;
}
.frmcont {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 4;
}

.dispNone {
    display: none!important;
}
.db {
    display: block !important;
}
.bezid, .blckbg {
    z-index: 4;
}

.ber-mcont {
    background: #fff;
    width: 984px;
    min-height: 500px;
    display: table;
    margin: 0px auto;
    font-family: arial;
    position: relative;
}
.oEq_r.ber-mcont {
    width: 810px;
    min-height: 500px;
}
.blHW.ber-mcont {
    background: #fff;
    width: 984px;
    min-height: 500px;
    display: table;
    margin: 0px auto;
    font-family: arial;
    position: relative;
}
.ber-mcontbl {
    background: #fff;
    width: 984px;
    min-height: 500px;
    display: table;
    margin: 0px auto;
    font-family: arial;
    position: relative;
}


.oEq_r * {
    box-sizing: border-box;
}
.redc{
    color: #f40c10 !important;
}
.oEq_r .nwInn {
    width: 230px;
    padding: 10px;
    border-radius: 5px 0 0 5px;
    flex-shrink: 0;
}
.oEq_r .ber-Lsc, .oEq_r .ber-Lsc1, .oEq_r .ber-Lsc2, .oEq_r .ber-Lsc3 .beclrW {
    color: #333;
}
.oEq_r .ber-Lsc, .oEq_r .ber-Lsc1, .oEq_r .ber-Lsc2, .oEq_r .ber-Lsc3 {
    background-color: #fafafa;
    color: #333;
    width: 300px;
}
.oEq_r * {
    box-sizing: border-box;
}
.ber-Lsc, .ber-Lsc1, .ber-Lsc2, .ber-Lsc3 {
    background: #4458a7;
    color: #fff;
    padding: 10px;
    vertical-align: top;
    position: relative;
    min-height: 500px;
}
.ber-Lsc {
    width: 500px;
}
.oEq_r .belodrbg {
    background: rgba(255, 255, 255, 0.5);
    height: 100%;
    position: absolute;
    top: 0;
    z-index: 5;
    width: 100%;
    right: 0px;
}
.oEq_r .nwInn .ber-prdimg {
    height: 280px;
    width: 280px;
    border: 1px solid #eaeaea;
    border-radius: 5px;
    margin: 0;
}
.ber-prdimg, .pr {
    position: relative;
}
.ber-prdimg::before {
    width: 100%;
    height: 100%;
    background-color: #fff;
    top: 0px;
    left: 0px;
    z-index: 1;
    position: absolute;
    content: "";
}
.oEq_r .ber-prdimg img {
    border-radius: 5px;
}

.ber-prdimg img {
    max-width: 100%;
    max-height: 100%;
    position: absolute;
    left: 0px;
    right: 0px;
    margin: auto;
    top: 0px;
    bottom: 0px;
    z-index: 3;
}
.oEq_r .lTxt {
    padding: 0 5px;
}
.oEq_r.eqPdsec .eprod {
    font-size: 16px;
    line-height: 1.3;
    font-weight: normal;
    color: #323232;
    font-weight: 500;
}
.oEq_r.eqPdsec .eqprodpr {
    padding: 0 0 5px;
    font-size: 15px;
}

.oEq_r .eqprodpr {
    font-size: 15px;
    color: #777;
    padding: 5px 0;
    margin-top: 10px;
}
.oEq_r.eqPdsec .eqpr {
    font-size: 18px;
    font-weight: 700;
}

.oEq_r .eqpr {
    font-size: 18px;
    color: #111;
    font-weight: 700;
}


.oEq_r .befs16 {
    font-size: 14px;
}
.pnsEnq {
    font-weight: bold;
    font-size: 15px;
    line-height: normal;
    margin-left: 25px;
    padding: 0;
    cursor: default;
    color: black;
}
.pnsEnq:before {
    position: relative;
    content: " ";
    margin-left: -30px;
    width: 22px;
    height: 22px;
    min-width: 22px !important;
    vertical-align: middle;
    display: inline-block;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNC43NSIgaGVpZ2h0PSIxNC41MTMiIHZpZXdCb3g9IjAgMCAxNC43NSAxNC41MTMiPjxkZWZzPjxzdHlsZT4uYXtmaWxsOiMwNjgwNzY7fTwvc3R5bGU+PC9kZWZzPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKC05MDguMjkxIC0zNjUuMTk1KSI+PHBhdGggY2xhc3M9ImEiIGQ9Ik05MTcuOSwzNzkuNzA4YTIuMjE3LDIuMjE3LDAsMCwwLDEuMzQ1LS40MTJjLjU1Mi0uMzk0LDEuMDgzLS44MjEsMS42LTEuMjYyYS44MjkuODI5LDAsMCwwLS4wMTYtMS4xMzRjLS41NzMtLjU5LTEuMTUzLTEuMTc0LTEuNzQ5LTEuNzQxYTEuMTA4LDEuMTA4LDAsMCwwLTEuNDA2LS4wNDVjLS4zMjQuMjI3LS42NTcuNDQ1LS45NTguN2EuNzY5Ljc2OSwwLDAsMS0uOTMxLjA4OSwzLjM1MSwzLjM1MSwwLDAsMS0uNzYtLjVjLS44NzUtLjgzMS0xLjczMi0xLjY4LTIuNTgzLTIuNTM2YTIuMzc1LDIuMzc1LDAsMCwxLS4zNjktLjU1NS45MTguOTE4LDAsMCwxLC4wOS0xLjFjLjI0Mi0uMy40Ni0uNjE5LjY4NS0uOTMzYTEuMDg2LDEuMDg2LDAsMCwwLS4xLTEuNDQxcS0uOC0uODE0LTEuNjE5LTEuNjEzYS44NzguODc4LDAsMCwwLTEuMzYuMDQzYy0uMzYxLjQxLS43LjgzOS0xLjAyNiwxLjI4YTIuMjUxLDIuMjUxLDAsMCwwLS4zLDIuMTkxLDkuOTc0LDkuOTc0LDAsMCwwLDEuODE1LDMuMDc2LDI2LjA0NywyNi4wNDcsMCwwLDAsNS4yMTUsNC45MTNBNC45NDUsNC45NDUsMCwwLDAsOTE3LjksMzc5LjcwOFoiLz48cGF0aCBjbGFzcz0iYSIgZD0iTTkxNi4wNzksMzY4LjhhNC4xMTksNC4xMTksMCwwLDEsMy4zNTYsMy4zNTUuNDI5LjQyOSwwLDAsMCwuNDI2LjM1OS40NDguNDQ4LDAsMCwwLC4wNzMtLjAwNy40MzMuNDMzLDAsMCwwLC4zNTYtLjUsNC45ODMsNC45ODMsMCwwLDAtNC4wNi00LjA2LjQzNC40MzQsMCwwLDAtLjUuMzUyLjQyOC40MjgsMCwwLDAsLjM0OS41Wm0wLDAiLz48cGF0aCBjbGFzcz0iYSIgZD0iTTkyMy4wMzQsMzcxLjg4N2E4LjIsOC4yLDAsMCwwLTYuNjg2LTYuNjg2LjQzMi40MzIsMCwwLDAtLjE0MS44NTIsNy4zMzEsNy4zMzEsMCwwLDEsNS45NzUsNS45NzUuNDMuNDMsMCwwLDAsLjQyNi4zNTkuNjIuNjIsMCwwLDAsLjA3NC0uMDA3LjQyNC40MjQsMCwwLDAsLjM1Mi0uNDkzWm0wLDAiLz48L2c+PC9zdmc+");
    background-position: 6px 2px;
    background-repeat: no-repeat;
}
.pnsno {
    margin-left: 5px !important;
}

.oEq_r .eqsoldby {
    font-size: 15px;
    color: #111;
    padding: 5px 0;
    line-height: 24px;
}
.oEq_r.eqPdsec .eqsold, .oEq_r.eqPdsec .eqsoldby {
    font-weight: normal;
    font-size: 16px;
}

.oEq_r .col777, .oEq_r .eqsold {
    color: #777 !important;
}
.col77{
    min-width: fit-content;
    color: #757575;;
}
.isqvalcls{
    font-weight: 500;
    color: #4c4c4c;
}
.compurlredir:hover{
    color: #1b1f91;
}
.compurlr{
    color: rgb(17, 17, 17);
    font-weight: 500;
}
.ber-Rsc.btPd {
    padding-bottom: 80px;
}

.oEq_r .ber-Rsc {
    padding: 25px 10px 15px 10px;
    /* box-shadow: -16px 0 13px 0 rgba(229, 229, 229, 0.16); */
    width: 330px;
    min-height: 500px;
    flex-shrink: 0;
    border-radius: 0 5px 5px 0;
}
.befs14, .ber-help, .ber-mfrm, .ber-Rsc {
    font-size: 14px;
}
.befs13 {
    font-size: 13px;
}
.ber-Rsc {
    background: #fff;
    position: relative;
    padding: 25px 20px 15px 20px;
    line-height: 18px;
    flex: auto;
}
.oEq_r .ber-cls-rec {
    width: 18px;
    height: 18px;
}
.ber-cls-rec, .ber-cross {
    font-size: 22px;
    top: 10px;
    right: 10px;
    font-style: normal;
    color: #666;
    z-index: 3;
    line-height: 18px;
    font-weight: bold;
}
.ber-frmpop .bedvh {
    min-height: 75px;
}
.oEq_r .enqLogIn {
    position: relative;
    z-index: 2;
    margin-bottom: 15px;
}
.oEq_r .ber-hdg-r {
    padding-right: 60px;
    padding-bottom: 5px;
}
.ber-hdg-r {
    font-size: 20px;
    padding: 15px 0 0 0;
    color: #000;
    line-height: 22px;
}
.otphdg {
    font-size: 20px;
    color: #000;
    padding-bottom: 15px;
    font-weight: 700;
  }
.befwt {
    font-weight: bold;
}
.newhdcls{
    display: flex;
    flex-direction: column;
    font-weight: bold;
    font-size: 24px;
}
.subhdcls{
    font-size: 14px;
}
.safetxt{
    font-size: 12px;
    color: #a7a7a7;
    font-style: italic;
    margin-left: auto;
}
.submtext{
    margin-top: 10px;
    font-size: 13px;
    color: #757575;

}
.oEq_r .ber-dvtxt, .oEq_r .ber-lbl {
    color: #111;
}
.oEq_r .enqLogIn .ber-input { width: 390px; height: 50px; border: 1px solid #c9c6c6; border-radius: 3px !important; padding-left: 65px; }
.ber-input, .ber-slbox {
    height: 34px;
    vertical-align: top;
    padding: 0px 0 0 8px;
    margin: 0px;
    font-size: 15px;
    border: 1px solid #c9c6c6;
    border-radius: 2px;
    background-color: #fff;
    color: #000;
}
.oEq_r .ber-slbox {
    border-radius: 7px !important;
}
.ber-slbox, .ber-fwd {
    width: 100%;
}
.loginName{
    height: 34px;
    vertical-align: top;
    padding: 0px 0 0 8px;
    margin: 0px;
    font-size: 15px;
    border: 1px solid #c9c6c6;
    border-radius: 3px;
    background-color: #fff;
    color: #000;
    width: 100%;
}
.oEq_r .hovsub {
    cursor: pointer;
}

.oEq_r .befstgo2 {
    margin-top: 10px;
}
.oEq_r .befstgo3, .oEq_r .befstgo2 {
    width: 200px;
    height: 45px;
    border-radius: 3px;
    background-color: #029f93;
    border: solid 2px #029f93;
    font-size: 16px;
    color: #fff;
    margin-top: 15px;
}
.oEq_r .col111 {
    color: #111;
}
.txt-cnt, .ber-mdl, .betxtc, .beotpR, .e_whm .ber-grbg {
    text-align: center;
}
.ber-txtarea {
    resize: none;
    height: 48px;
    font-size: 15px;
    font-family: arial;
    line-height: 18px;
    padding: 5px 9px;
    margin: 0;
    outline: 0;
  }

  .form-group input[type="text"]::placeholder, .form-group input[type="email"]::placeholder{
    font-size:14px
}
.eqTstR .inPlace::placeholder {
    font-size: 13px;
  }


.ber-cls-rec, .ber-cross {
    font-size: 22px;
    top: 10px;
    right: 10px;
    font-style: normal;
    color: #666;
    z-index: 3;
    line-height: 18px;
    font-weight: bold;
}
.ortxt{
    margin-top:10px;
    font-size:14px;
    font-weight:600;
}
.beabult, .ber-frmpop .nwarN, .ber-mfrm .nwarN, .ber-cls-rec, .int-ct, .int-ct1, .int-ct3, .inft, .othrwd, .ber-erbx, .ber-erarw, .beerrp, .beerrp4, .beerrp5, .beerrp6.beerrp1, .beerrp2, .beerrp3, .ber-cross {
    position: absolute;
}
.ber-lbl {
    padding: 0 0 9px 0;
    text-align: left;
    margin-right: 1px;
    background: 0 0;
    font-weight: 400;
    font-size: 15px;
    color: #696969;
    pointer-events: none;
    line-height: 13px;
    display: block;
}
.cntry_sugg { top: 50%; z-index: 1; transform: translateY(-50%); left: 15px; width: 40px; height: 30px; position: absolute; text-align: center; border-radius: 3px; line-height: 30px;background-color: #eee;}
.oEq_r .beerrp {
    position: unset;
    color: #d6181b;
    padding: 5px 0;
    background: transparent;
    border: none;
    font-weight: normal;
}
.bemlsecR {
    background: #fff;
    margin: 25px 0 10px 0;
}
.bemlsecR3 {
    background: #fff;
    margin: 0px 0 10px 0;
}
.mt10{
    margin-top: 10px;
}
.mt20{
    margin-top: 20px;
}
.mt15{
    margin-top: 15px;
}
.mt-15{
    margin-top: -15px;
}
.cp{
    cursor: pointer;
}
.ber-lbl{
    margin-top: 15px;
}
.txt-cnt {
    text-align: center;
}

button
/* inline form css */
.mb15 {
    margin-bottom: 15px;
}

.bgin15 {
    background: #241f55;
    font-size: 18px;
    color: #fff;
    padding: 8px 10px;
    border: 1px solid #242056;
}
.bxs, .contlk, .pdin, .wful, .wid1 {
    box-sizing: border-box;
}
.mt5 {
    margin-top: 5px;
}
.mt7{
    margin-top: 7px;
}
.mb8 {
    margin-bottom: 8px;
}
.dtbl {
    display: table;
}
.pbdrb {
    border-bottom: 1px solid #eaeaea;
}
.fs12 {
    font-size: 12px;
}
.frmimg {
    width: 70px;
    height: 80px;
    bottom: 52px;
    left: 10px;
    position: absolute;
}
.belft {
    float: left;
}

.frmimg img {
    max-width: 60px;
    max-height: 60px;
}
.pdgbt {
    color: #fff;
    border: 1px solid #058b80;
}
.pdgbt {
    background: #058b80;
}


/*  Sign in with Google */
.buttonTextfr {
    display: inline-block;
    vertical-align: middle;
    padding-left: 18px;
    padding-right: 18px;
    font-size: 16px;
    font-weight: bold;
    font-family: sans-serif;
}
span.Gicon {
    background: url(//utils.imimg.com/globalhf/Glogo.png) white 50% 50% no-repeat;
    display: inline-block;
    vertical-align: middle;
    background-size: 22px 22px;
    width: 42px;
    height: 42px;
}
.customG {
    display: inline-block;
    background: #4685f5;
    color: #fff;
    width: auto;
    border-radius: 0px;
    border: thin solid #4685f5;
    box-shadow: 1px 1px 1px grey;
    white-space: nowrap;
    cursor: pointer;
}
.ber-erarw, .ber-erbx {
    background: #ffeaea;
}
.ber-erarw {
    border-left: 0;
    border-top: none;
    border-right: 1px solid #ffeaea;
    border-bottom: 1px solid #ffeaea;
    transform: rotate(-315deg);
    width: 10px;
    height: 10px;
    top: 85%;
    left: 17%;
}
.ber-inbl, .bedblk, .disp-inl {
    display: inline-block;
}
.m0{
    margin: 0;
}
a, .att, .att2 {
    text-decoration: none;
}
.rn{
    resize: none;
}
@keyframes circleAnimation {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}
@keyframes drawTick {
    0% {
        opacity: 0;
        stroke-dashoffset: 50;
    }

    100% {
        opacity: 1;
        stroke-dashoffset: 0;
    }
}
.circle {
    position: relative;
    width: 40px;
    height: 40px;
    background-color: #00A58E;
    border-radius: 50%;
    opacity: 0;
    animation: circleAnimation 1s ease-in-out forwards;
}
.pflx1 {
    flex: 1;
}
.thbtmnR {
    display: flex;
    width: auto;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    flex-direction: column;
    font-size: 13px;
    color: #5c5c5c;
}

.thbtmn {
    display: flex;
    width: auto;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    box-sizing: border-box;
    flex-shrink: 0;
    transition: box-shadow 0.3s ease 0s;
    min-width: 205px;
    justify-content: center;}

.chtBtn, .slrIcn {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAhCAMAAABp0ZInAAAAllBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////AJcWoAAAAMXRSTlMAP0IyM/rvS1I8NeUFJQ7zajQYBN3KFQj36KqAcz4e0c+7sZiIel5aQ9fHLdLDpGBEYhheowAAARhJREFUOMuN0NdugzAYhuEPKA61WSGEETZhJeni/m+ujTjAjNp5jhB6ZX36AViZspJZWOuYPq7otw5L1s031JXeZ6vXMr3HhqFnWFBGFRvqqLxQudHwQhW24f+VR5Ut6i0r6jvjlnOnfOXdmatupcz3uIo6Kfa4Dp2r6cs03nj09PyvrCuDjDxi7Fano8YzzKmikctVW1NlM/aVA/k1EO3Co9IT5OeoF+0CKOnsS5wIdwFH8t2QVLRrqhJyDcX3An5IXHkAJLtY8ZDd688hl95rdnRc7Gkdilngx+2BNwQArIYVATjaJ9EXqhCoo0LDQqC989LngS4fFoTMhri4nG2I2TUb5BWs8lzLKxxiUsorJKQ0Iad4AH4BzFUnf7RUVkUAAAAASUVORK5CYII=) no-repeat;
    width: 27px;
    height: 23px;
    background-size: 100%;
    margin-right: 10px;
}
.id_aic, .e_whm .eqBksb {
    align-items: center;
}
.idsf {
    display: flex;
}
.befs16, .eqs16 {
    font-size: 16px;
}
.eqEcmp li > div {
    background-color: #fff;
    border: solid 1px #dadada;
    border-radius: 9px;
    padding-bottom: 6px;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
    height: 100%;
}
.befs11 {
    font-size: 11px;
}
a.eComtxt:hover, a.ectxt63:hover {
    color: #da2931;
}
.plaGrd {
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    gap: 10px;
}
.otpsub.submit-button-img{
    margin-top: 10px!important;
}
.submit-button-img{
    background: linear-gradient(180deg, #05877c 0%, #03675e 100%);
    width: 57%;
    border: none;
    border-radius: 6px;
    font-size: 22px;
    height: 52px;
    cursor: pointer;
    margin-top: 10px;
    transition: box-shadow 0.3s ease 0s;
    color: #fff;
    outline: 0;
    font-weight: bold;
    margin-top:10px;
}
.submit-button-img:hover{
    box-shadow: 0 0px 1px 0 #ddd, 0 1px 0px 1px #00423d;
    background-color: #007a6e !important;
    border-color: #007a6e !important;
}  
.form-group-img{
    text-align: center;
}
.mb15{
    margin-bottom: 15px;
}
option {
    color: #000;
}
input[type="text"] {
    background-color: white;
}
.frmcont input[type="text"]:focus {
    border: 1px solid ;
    border-color: #029f93!important;
}

/* .stsec{
    z-index: 8;
} */

.mb-6{
    margin-bottom: -6px
}
.m-10{
    margin: -10px
}
.befs13 {
    font-size: 13px;
}
.bcw{
    background-color: white;
}

#t0901_mcontR input[type="submit"],#t0901_mcontR button[type="submit"] {
    font-size:20px !important;
    font-weight: bold;

}
#t0901_mcontR button[type="submit"].pricewidgetloginscr{
    font-size: 16px !important;
}
.eqElps1 {
    line-clamp: 1;
    -webkit-line-clamp: 1;
}
.isqbtm{
    border-bottom: 1px solid #dadada;
}
.prodsrchtitl {
    padding: 0 0 9px 0;
    text-align: left;
    margin-right: 1px;
    background: 0 0;
    font-weight: 400;
    font-size: 15px;
    color: #696969;
    pointer-events: none;
    line-height: 13px;
    display: block;
}
.inEqlRec .prodsrchtitl {
    color: #111;
}
.dropdown dd ul {
    width: 302px !important;
    z-index: 99;
    top: 8px;
    letter-spacing: -0.08px;
  }
  .eqCntry {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 50px;
    height: 34px;
    border-radius: 3px;
    background-color: #eaeaea;
}

  .oEq .eqCntry .dropdown dt a span {
    width: 60px;
    background-repeat: no-repeat;
  }

  .country_drop_parent_cont{
    margin: 7px 0px;
    display: flex;
  }
  .country_drpn{
    position: absolute;
    display: inline-block; 
    width: 300px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    padding: 8px 0;
    z-index: 5;
  }
  .country_drpn ul{
    overflow-y: scroll;
    list-style: none;
    background-color: #fff;
    border-top: 1px solid #ddd;
    padding: 8px;
    margin-top: 8px;
    max-height: 155px;
    /* text-align: left; */
  }
.country_drpn ul li span {
    width: 16px;
    height: 11px;
    background: url(https://utils.imimg.com/imsrchui/imgs/country-v5.png);
    display: inline-block;
    margin-right : 5px;
}
.country_drpn ul li:hover{
    background-color: #bebebe;
}
.country_drpnclick{
    display: flex;
    cursor: pointer;
    color: #698181;
    font-size: 12px;
    width: max-content;
}
.country_drpnclick p{
    color: #b0b4bb;
}
.selected_cont{
    margin-left: 5px;
    font-weight: 700;
    text-decoration: underline;
    cursor: pointer;
}
.emailbox:before {
    content: "";
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAQCAMAAAA7+k+nAAAAY1BMVEUAAACOjo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo7wJaXWAAAAIHRSTlMA3h4RVfj0azQXBufNtqumfVtHDE3u2dTFv5qQPioiTlruRzgAAACeSURBVBjTdc1XEoQgEEXRpw6oqCRzHPa/yqG1LNEpb/FDHwKil+Cc+vylnIPTLMWjlGkPseUzbs3cxgTICimusZBFhgMQ90NyzpOhpwkB7cZuw97WjQkugFD+vi8rlEAIMI5/gYY7gxtoZlKW+mWYDkBM7ULHeQMs7SROqKto3T/Yv1mjqj4gL8scQbQnsHQijF6wHpjMk0e5ZB5e+gE5LA1aMmuKKgAAAABJRU5ErkJggg==") 50% no-repeat;
    height: 20px;
    width: 28px;
    left: 10px;
    top: 7px;
    position: absolute;
}
.input-containermob .emailbox:before {
    left: 7px;
    top: 11px;
}
.emailbox-enq{
    content: "";
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAQCAMAAAA7+k+nAAAAY1BMVEUAAACOjo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo6Ojo7wJaXWAAAAIHRSTlMA3h4RVfj0azQXBufNtqumfVtHDE3u2dTFv5qQPioiTlruRzgAAACeSURBVBjTdc1XEoQgEEXRpw6oqCRzHPa/yqG1LNEpb/FDHwKil+Cc+vylnIPTLMWjlGkPseUzbs3cxgTICimusZBFhgMQ90NyzpOhpwkB7cZuw97WjQkugFD+vi8rlEAIMI5/gYY7gxtoZlKW+mWYDkBM7ULHeQMs7SROqKto3T/Yv1mjqj4gL8scQbQnsHQijF6wHpjMk0e5ZB5e+gE5LA1aMmuKKgAAAABJRU5ErkJggg==") 50% no-repeat;
    height: 20px;
    width: 28px;
    /* left: 121px;
    top: 58px;
    position: absolute; */
}
.oeWicn {
    width: 14px;
    height: 14px;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAMAAAAolt3jAAAANlBMVEUAAAB2iIh0h4d2iIh0h4d2h4dwgIB1iYl1iIh2iYl2iYl2h4d1h4d2iYl1iIh2iIh2hoZ2iIjEqp3/AAAAEXRSTlMAnyDfQIAQcL+v78+QUDCPUBAH8BAAAABoSURBVAjXNY5JDgNBCANNQ2+zJf7/Z2ONlTqACiQMIGpG9AYzNldci/EOWp6lNskc6sEbeL4UW0uyo2gKnczPc1gngiJpQvpytf7qtN5KP/5XHLKo5G1fGBmAqr3O9Fth3wOm9YhZED+1BAbG9lstIQAAAABJRU5ErkJggg==") no-repeat;
    margin-right: 5px;
    margin-top: 1px;
    flex-shrink: 0;
}
.contsearch{
    height: 24px !important;
    border-radius: 3px !important;
    border: 1px solid #000 !important;
    font-size: 14px !important;
    padding: 0 10px !important;
    margin: 5px 8px !important;
    width: 163px !important;
}
.flisq{
    background: transparent !important;
    border: none;
    width: 50px;
    text-align: center;
    padding: 10px 0;
}
.GDPRerr{
    color: #d6181b;
    font-style: italic;
    font-size: small;
}
.chckbx{
    width: 15px !important;
    height: 15px !important;
    vertical-align: middle ;
    margin-right: 3px;
    display: inline-block;
}
.blpopgo{
    top: -80px;
    left: 440px;
    width: 80px;
    position: relative;
}
.name-input-inlinebl:focus{
    border: 1px solid #2e3192;
}
.blpopgoerr{
    top: -108px;
}
.blpopgofrn{
    top: 5px;
    left: 302px;
}
.blpopgoin{
    text-align: left;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAUCAMAAABRYFY8AAAAflBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////vroaSAAAAKXRSTlMABXz4Pwkm6cCnli8TDdKxj2c3HRfi2M+biXhzcF9VT0pHKt7Ht5+FXngowu8AAACiSURBVBjTbZBXDoQwDEQTUgiE3lk6W33/C64gokjO+8uzFI+HcEmsJNBqavEPAIiXAHkHNqK0QN5QfiVFfoc1XFzehxvv8Vw0xbULF3V/5KZhvs6p06inmbh+ZhZdc80HjwGwVypuXgRrNu4+6sPtXUj+65I4Yuafzx5r8VTJ4KRyJMU51VDgu8CbQ3yvm2hLD1WXU9yzmnDPLfiZIBiuiY0/gdsYqyaf6k0AAAAASUVORK5CYII=') no-repeat;
    color: #fff;
    padding: 0px 0px 0px 14px;
    border-radius: 60px;
    width: 82px;
    font-size: 20px;
    border: none;
    background-position: 46px 7px;
    line-height: 34px;
    height: 34px;
    background-color: rgb(0, 166, 153);
}
.enqpopgoin{
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAUCAMAAABRYFY8AAAAflBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////vroaSAAAAKXRSTlMABXz4Pwkm6cCnli8TDdKxj2c3HRfi2M+biXhzcF9VT0pHKt7Ht5+FXngowu8AAACiSURBVBjTbZBXDoQwDEQTUgiE3lk6W33/C64gokjO+8uzFI+HcEmsJNBqavEPAIiXAHkHNqK0QN5QfiVFfoc1XFzehxvv8Vw0xbULF3V/5KZhvs6p06inmbh+ZhZdc80HjwGwVypuXgRrNu4+6sPtXUj+65I4Yuafzx5r8VTJ4KRyJMU51VDgu8CbQ3yvm2hLD1WXU9yzmnDPLfiZIBiuiY0/gdsYqyaf6k0AAAAASUVORK5CYII=') no-repeat;
    background-position: 180px 12px;
    line-height: 34px;
    width: 260px !important;
    border-radius: 8px !important;
}
.enqpopgoin.pricewidgetloginscr{
    background-position: 245px 12px;
    width: 395px !important;
}
.pricewidgetloginscr{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    background-image: unset;
}
.errcls{
    color: #333;
    border: 1px solid #ffeaea;
    border-radius: 4px;
    line-height: 16px;
    padding: 5px 17px;
    white-space: nowrap;
    z-index: 3;
    font-size: 12px;
    font-weight: 700;
}
.inblerr{
    top: -27px;
    left: 60px;
}
.prodnmerr{
    top: -27px;
    left: 150px;
}
.prodnmerrpopup{
    top: -13px;
    left: 150px;
}
.inenqerr{
    top: 20px;
    left: 150px;
}
.p15_25 {
    padding: 25px;
}
@media screen and (max-width: 1280px) and (min-width: 990px) {
    .submit-button-img{
        width: 70%;
    }
}

.posR{
    position: relative;
}

.ber-mcont.lrSplit{
    background:transparent !important;
    min-height: unset!important;
}

.lrSplit .splitid{
    gap:8px
}

.lrSplit .ber-Rsc, .lrSplit .ber-Lsc{
    border-radius: 12px;
}
.aligncls{
    display: flex;
    flex-direction: column;
}
.lrSplit #t0901_rightsection,.lrSplit #t0401_rightsection{
    align-items: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.lrSplit #t0901screen3,.lrSplit #t0401screen3,.lrSplit .isqSpl{
    display: flex;
    justify-content: center;
    height: 100%;
    align-items: center;
}
.contactinfoshow #t0901screen3,.contactinfoshow #t0401screen3,.contactinfoshow .isqSpl{
    height: auto;
}

.lrSplit #t0901clslog,.lrSplit #t0401clslog,.lrSplit .isqDv,.lrSplit .contactdtlcls,.lrSplit #mdDiv{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.lrSplit .isqDv{
    padding: 0 10px;
}
.lrSplit .ber-hdg-r{
 text-align: center;
 padding-right: 0;
 /* margin-top:-50px; */
}

.lrSplit .blotp{
    text-align: center!important;
}

    .lrSplit .detect-button{
        right: 2px !important;
        top: 48px !important;
    }

 .lrSplit .rdDv{
    display: flex;
    flex-direction: column;
    align-items: center;
 }   
 .contactinfoshow .rdDv{
    flex: unset;
 }   


 .lrSplit .nonImg{
    text-align: center;
 }

 .lrSplit .ber-txtarea.rn,.lrSplit .widt100,.lrSplit .txtblng{
    width: 100% !important;
 }

 .lrSplit .betarea{
    width:80%
 }

.lrSplit .lgcont{
    margin: 0 auto;
}

.lrSplit .errorOtpR {
    width: 270px;
}

.lrSplit.oEq_r .befstgo3, .lrSplit.oEq_r .befstgo2,.lrSplit .submit-button,.lrSplit .getotpcls{
    background-color: #005E55;
    border: solid 2px #005E55;
    transition: box-shadow 0.3s ease 0s;
}

.lrSplit.oEq_r .befstgo3:hover, .lrSplit.oEq_r .befstgo2:hover,.lrSplit .submit-button:hover,.lrSplit .getotpcls:hover{
    background-color: #005E55!important;
    border: solid 2px #005E55!important;
    box-shadow: 0 0px 1px 0 #ddd, 0 1px 0px 1px #00423d;
}
.lrSplit.oEq_r .pricewidgetloginscr{
    background-color: #0D9488!important;
    border: solid 2px #0D9488!important;
}



.lrSplit #t0901screen1 #t0901_label-l{
    display: none;
}
.lrSplit #tqut_id{
    margin-bottom: 20px;
}
.lrSplit .phoneParent label {
    display: none;
}
.contact-button-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
  }