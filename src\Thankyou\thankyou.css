.BL_Thnku.thAutoM, .oEq_r.eqtstR.thAutoM, .oEq_r.ber-mcont.thAutoM, .BL_Thnku.oEq_r.ber-mcont.thAutoM {
    min-height: auto !important;
    display: flex;
}
.fs15{
    font-size: 15px;
}
.BL_Thnku.oEq_r.ber-mcont {
   
    min-height: 500px;
    box-sizing: border-box;
}
.BL_ThnkuRR.thAutoM, .oEq_r.eqtstR.thAutoM, .oEq_r.ber-mcont.thAutoM, .BL_ThnkuRR.oEq_r.ber-mcont.thAutoM {
    min-height: auto !important;
    display: flex;
}
@media only screen and (min-width: 1515px){
    .BL_Thnku.oEq_r.ber-mcont {
        min-height: 604px;
        width: 1150px;}
    .oEq_r.ber-mcont {
        width: 960px;
        min-height: 600px;}
    .BL_ThnkuRR.oEq_r.ber-mcont {
        min-height: 604px;}
}
.BL_ThnkuRR.oEq_r.ber-mcont {
    min-height: 500px;
    box-sizing: border-box;
}
.frmscroll.ber-mcont {
    max-height: 99.5vh !important;
    overflow-x: hidden;
    overflow-y: auto;
    align-items: flex-start;
}
.futhkCss {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0 0;}
.ber-mcont {
    background: #fff;
    width: 984px;
    min-height: 500px;
    display: table;
    margin: 0px auto;
    font-family: arial;
    position: relative;}
.plf6010 {
    padding: 0 60px 10px;}
.plf35 {
        padding: 0 25px 10px;}

        
    
.ebrtbc {
    border-bottom: 1px solid #ccc;}
.id_aic, .e_whm .eqBksb {
    align-items: center;}
span.an {
        margin-left: 9px;
        font-size: 24px;}
    
.bethhdg {
    font-size: 22px;
    color: #111;
    line-height: 21px;
    font-weight: normal;
    margin-top: 15px;}
.xMt10 {
    margin-top: 10px;}
.txt33 {
    color: #333;}
.txt16 {
    font-size: 16px;}
.cNlink {
    color: #007C72;}
.cNlink a {
    color: #007C72;
    text-decoration: underline 2px;}
.circleR {
    position: relative;
    width: 32px;
    height: 32px;
    border: 2px solid white;
    background-color: #00A58E;
    border-radius: 50%;
    opacity: 0;
    animation: circleAnimation 1s ease-in-out forwards;}

    .circle {
        position: relative;
        width: 40px;
        height: 40px;
    }    
.thYTxt {
    margin-left: 9px;
    font-size: 17px;
    line-height: 23px;
    color:#434343;}

.pdNm{
        font-weight: 600;
    }
.ejcsv {
    justify-content: space-evenly;}
.eptb10 {
    padding: 10px 0;}
.idsf {
    display: flex;}
.oEq_r * {
    box-sizing: border-box;}
.bemb5 {
    margin-bottom: 5px;}
.thbtmnR {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    flex-direction: column;
    font-size: 13px;
    color: #5c5c5c;}

.thbtmn {
    display: flex;
    width: auto;
    align-items: center;
    justify-content: space-between;
    text-align: center;
    box-sizing: border-box;
    flex-shrink: 0;
    transition: box-shadow 0.3s ease 0s;
    min-width: 205px;
    justify-content: center;}

.befwt {
    font-weight: bold;
}
.tvwBtnR, .tsnBtnR {
    position: relative;
    padding: 11px 25px;
    color: #fff;    
    text-transform: capitalize;
    border-radius: 8px;
    text-align: center;
    font-weight: 700;
    box-sizing: border-box;
    flex-shrink: 0;
    transition: box-shadow 0.3s ease 0s;
    /* min-width: 205px; */
    width: 100%;
    justify-content: center;}

.tvwBtnR{
    background-color: #00a699;
}

.tvwBtn, .tsnBtn {
    position: relative;
    padding: 11px 25px;
    color: #fff;
    background-color: #00a699;
    text-transform: capitalize;
    border-radius: 8px;
    text-align: center;
    transition: box-shadow 0.3s ease 0s;
    min-width: 205px;
    justify-content: center;}

    
.bemt5 {
    margin-top: 5px;}
.pdv20R {
    padding: 10px 60px 6px !important;}

    .pdv20 {
        padding: 12px 60px 6px !important;}

        .pdv20adv {
            padding: 12px 20px 6px !important;}
    

.ebgF3 {
    background-color: rgba(0, 0, 0, 0.05);}
.eqmt5 {
    margin-top: 5px;}
.BL_Fm8 {
    margin-bottom: 10px;}
.beclr3 {
    color: #333;}
.plaGrd {
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    gap: 10px;}
.plaADV {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 10px;}
.eqEcmp li,.eqEcmpF li {
    height: 245px;
    list-style: none;
    position: relative;}
.eqEcmpF.plaADV li {
    height: unset;
    width: 360px;
}
.eqEcmp li > div,.eqEcmpF li > div {
    background-color: #fff;
    border: solid 1px #dadada;
    border-radius: 9px;
    padding-bottom: 50px;
    overflow: hidden;
    display: block;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
    height: 100%;}
.eqEcmp li:hover > div {
    left: 0;
    position: absolute;
    height: 105%;
    top: -2%;
    width: 103%;
    left: -5px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    z-index: 2;
}
.eqEcmp li:hover .ecmpImgF img {
    max-height: 140px;
}
.fs0{
    flex-shrink: 0;
}
.ecmpImgF {
    max-width: 100%;}
.ecmpImgF a {
    height: 120px;
    padding-top: 6px;}
.ecmpImgF a img {
    max-height: 120px;
    max-width: 100%;}
.ecmpImgadv {
    width: 120px;
    height: 120px;
}
.ecmpImgadv a {
    height: 120px;
    width: 120px;
    padding-top: 6px;
}
.ecmpImgadv a img {
    max-height: 100%;
    max-width: 100%;}
.eqEcmp li .easFd,.eqEcmpF li .easFd {
    width: 'unset';
flex: '1';
padding-right: 0;}
.easFd {
    align-self: flex-end;}
.asFS {
    align-self: flex-start !important;
text-align: left;}
.eplh16 {
    line-height: 16px;}
.eplf6 {
    padding: 2px 9px 3px 9px;}
.txtElp {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;}
.txtElp1 {
    -webkit-line-clamp: 1;
}
.pdpU {
    min-height: 32px;
    font-weight: 700;
    font-size: 11px;}
    .pdpadv {
        font-weight: 700;
        font-size: 11px;
    margin: 7px;}
    a.eComtxt {
    color: #2e3192;}
    
.ectxt63 {
    color: #636363;}
.emBynw {
    background-color: #007C72;
    color: #fff;
    font-size: 13px;
    width: 90%;
    padding: 8px 0;
    border-radius: 4px;
    text-align: center;
    margin-top: 5px;
    display: block;}

.emBynw.adv {
    font-size: 16px;
    margin: 5px auto;
    position: absolute;
    left: 50%;
    bottom: 5px;
    transform: translateX(-50%);
}
.emBynw.adv:hover{
    box-shadow: 0 0 1px 0 #ddd,0 0.5px 0 0.5px #00423d;
}
.ew50 {
    width: 50%;}
.sellBll {
    color: black;
    font-size: 16px;
    /* font-weight: 700; */
}
.sellBl {
    color: black;
    font-size: 16px;
    font-weight: 700;
}
.tsnBtnR {
    /* background-color: #ffffff; */
    /* border: 1px solid #058177; */
    /* color: #058177; */
    /* padding: 11px 10px; */
    color: rgba(0, 166, 153, 1);  
    text-decoration: underline;
}

.tsnBtn {
    background-color: #ffffff;
    border: 1px solid #058177;
    color: #058177;
    padding: 11px 10px;
}

/* .oEq_r .ber-cls-rec {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAMAAADarb8dAAAAOVBMVEUAAAAQEBAGBgYQEBADAwMMDAwFBQYGBgYMDAwODg4HBwkLCwsICAoODg4FBQYICAgHBwcKCgoAAADrDwfOAAAAEnRSTlMAIN8Q72Cgz18wz5B/QM+/r1DSrd+vAAAAWElEQVQI113PRw4AIQgFUJApTnEK9z+smk8ggY08JIJEfBPCUi66gZauqvqJWQujckoYlV2OBfYKbDFv3d6jLdxSRx168AaMKT61pj3+4Tc2lfyXeVygpR2whwVoSILV8wAAAABJRU5ErkJggg==) no-repeat center center;
    width: 18px;
    height: 18px;
} */
.ber-cls-rec, .ber-cross {
    font-size: 22px;
    top: 10px;
    right: 10px;
    font-style: normal;
    color: #666;
    z-index: 3;
    line-height: 18px;
    font-weight: bold;
}
.plf40 {
    padding: 0 35px 30px;
}

.tick {
    position: absolute;
    top: 50%;
    left: 50%;
    opacity: 0;
    stroke-dasharray: 50;
    stroke-dashoffset: 50;
    animation: drawTick 1s ease-out 1s forwards;
}
.ths_w {
    width: 100%;
}
.bgcol{
    background-color:rgba(241, 243, 244, 1);

}

#pla-strip {
    display: grid;
    grid-template-columns: repeat(4,1fr);
    grid-auto-rows: 1fr;
    grid-gap: 16px;
}

#pla-strip .cardsPla{
    display: grid;
    border-radius: 10px;
    border-left: 6px solid;
    grid-template-columns: 80px auto;
    box-shadow: 3px 0 6px rgba(0,0,0,.12);
    transition: .3s;
    height: 100%;
    color: #2e3192;
    align-items: center;
    background-color: #fff;
    cursor: pointer;
}

#pla-strip .cardsPla:hover{
    box-shadow: 0 5px 17px 0 #d5d5d5;
}

#pla-strip .ecmpImgR {
    align-items: center;
    justify-content: center;
    display: flex;
    height: 80px;
    width: 80px;
    margin-left: 3px;
}

#pla-strip .ecmpImgR img{
    height: 75px;
    width: 75px;
}

#pla-strip .cardTxt {
    margin: 4px 0 4px 10px;
    padding-right: 5px;
}

#pla-strip .namePla{
    font-weight: 700;
    /* color: #333; */
    -webkit-line-clamp: 2;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 13px;
    text-decoration: underline;
}

.linecl_pdp{
        display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    color: rgba(0,0,0,.6);
    margin-top: 3px;
    font-size:14px;

}

.verLink {
    text-decoration: underline;
    color: #007C72;
    font-weight: bold;
    cursor: pointer;
}

.shd {
    -webkit-filter: drop-shadow(rgba(0, 0, 0, 0.3) 0px 1px 2px);
}

.zz {
    position: relative;
}

.zz:after {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    height: 10px;
    background-size: 20px 100%;
    background-position: center;
    bottom: -9px;
}

.zg:after {
    /* top: 100%; */
    background: linear-gradient(135deg, #F1F3F4 33.333%, transparent 0), 
                linear-gradient(-135deg,#F1F3F4 33.333%, transparent 0);
    background-size: 20px 10px; 
    background-repeat: repeat-x;
}


.order-details {
    margin: 0 30px 0 15px;
    color: grey;
    padding: 15px 8px;
    background: rgba(241, 243, 244, 1);
    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.16);
}
.order-detail-text {
    display: inline; /* Keeps everything inline */
    line-height: 21px;
}

.order-detail-text.clmp {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    }

.order-detail-item {
    display: inline; /* Keeps items inline */
    margin-right: 1rem; /* Space between each item */
}

.order-detail-key {
    font-weight: bold; /* Bold for keys */
    margin-right: 0.5rem; /* Space between each item */
}

.order-detail-value {
    color: rgba(133, 133, 133, 1);
}

.topDv {
    padding: 5px 20px 15px 20px;
    background: rgba(184, 230, 226, 1);
    margin-right: 15px;
    margin-top: 10px;
    border-radius: 8px;
}

#callNum {
    width: 100%;
    margin-bottom: 10px;
    background-color: #ffffff;
    border: 1px solid rgb(154 153 153);
    color: rgb(67, 67, 67);
    padding: 11px 10px;
    text-decoration: none;
}

#dtlMsg{
    padding: 5px 5px 5px 0;
    color: '#434343';
}

#cmpNm {
    font-weight: bold;
    font-size: 18px;
}
.rightTop{
    margin-bottom: 10px;
    line-height: 21px;
}

.dvBorder {
    max-width: 291px;
    padding: 20px;
    box-shadow: 0 5px 17px 0 #d5d5d5;
    margin-top: 10px;
    align-self: flex-start;
}
.BL_Thnku.oEq_r.ber-mcont {
    min-height: 604px;
    width: 1150px;}

@media screen and (max-width: 1280px) and (min-width: 990px) {
    .BL_ThnkuRR {
      width: 90vw !important;
    }
    .BL_Thnku.oEq_r.ber-mcont {
        min-height: 604px;
        width: 1150px;}
        .eqEcmpF.plaADV li {
            height: unset;
            width: 360px;
        }
        .flxwrp{
            flex-wrap: wrap;
        }
}  

@media only screen and (max-width: 1199px) {
  
    .BL_Thnku.oEq_r.ber-mcont {
        width: 980px;
    }
    .eqEcmpF.plaADV li {
        width: 300px;
    }
    .ecmpImgadv {
        width: 100px;
        height: 100px;
    }
    .ecmpImgadv a {
        height: 100px;
        width: 100px;
        padding-top: 6px;
    }
    .flxwrp{
        flex-wrap: wrap;
    }
}

.BL_ThnkuRR{
    width: 85vw !important;
}
.verStatus{
    display: flex;
    align-items: center;
    margin-left: 40px;
    font-size: 14px;
}

.verStatus svg{
    margin-left: 10px;
    margin-right: 3px;
}

.headkey {
    color: #676666;
    font-size: 16px;
    line-height: 20px;
}

#sellr_rtng.sllrTph1F .tcundf{margin-top: 15px;}
#sellr_rtng.sllrTph1F{margin-top: auto;}
.tcundf{
    font-size: 13px;
    text-decoration: underline;
}
.tchov1F {
    color: #2e3192;
}
.stWid{
    color:#333;
    font-weight: 500;
}
.sellercls{
    font-size: 12px;
    color: #848484;
    display: inline-block;
    position:relative;
}
.sRtF {
    unicode-bidi: bidi-override;
    color: #ccc;
    margin: 0;
    padding: 0;
}

.sRtF .flsRtF {
    color: #fdc12a;
    padding: 0;
    z-index: 1;
    top: 0;
    overflow: hidden;
    bottom: 0;
    left: 0;
}
.sRtF .flsRtF span {
    word-break: normal;
}
.sRtcls{
    display: inline-block;
    font-size: 16px;
    text-align: left;
}

.equVs,
.equVPs,
.equTs {
  width: 20px;
  height: 22px;
  margin: 0 3px 0 0;
}
.equVPs {
  background-position: -6px 0;
}
.equVs{
  background-position: -41px -25px;
}
.equTs {
  background-position: -6px -25px;
}
a.cityadv{
    color: #111;
}
.cityadv{
    margin:3px 0px;
}
a.cityadv:hover .cnmAdv{
    text-decoration:underline;
  }
.clrgry{
    color: #696969;
}
#plawid .a{cursor:pointer;}
.advidsf{
    margin:7px;
    margin-top:0;
}
.abslt {
    position: absolute;
}
.mr5 {
    margin-right: 5px;
}
.pd_flsh {
    flex-shrink: 0;
}
.fsblck{
    font-size: 11px;
    color: black;
}
.fw500{
font-weight: 500;
}
.scaleicon{
    transform: scale(0.8);
}
.inlBlock{
    display: inline-block;
}
.sellericons {
    background-image: url(https://apps.imimg.com/gifs/seller_Icons.png);
    background-repeat: no-repeat;
    /* background-size: 37px; */
    /* height: 15px; 
    width: 15px; */
}

.tvfSlr {
    background-position: 0px -114px;
    height: 15px;
    width: 15px;
    margin-right: 3px;
    background-size: 44px;
}

.vpsSlr{
        background-position: 0px -92px;
        height: 15px;
        width: 15px;
        margin-right: 3px;
        background-size: 44px;
  
}

.vsSlr{
    background-position: -27px -92px;
    height: 15px;
    width: 15px;
    margin-right: 3px;
    background-size: 44px;

}

.veSlr{
    background-position: -27px -70px;
    height: 15px;
    width: 15px;
    margin-right: 3px;
    background-size: 44px;

}

.memSinc {
    background-position: 0px -66px;
    height: 15px;
    width: 15px;
    margin-right: 2px;
    background-size: 42px;
}

.respRt{
        background-position: -19px -38px;
        height: 15px;
        width: 15px;
        margin-right: 3px;
        background-size: 37px;
   
}

.cityPoint{
    background-position: 0px -38px;
    height: 15px;
    width: 15px;
    margin-right: 2px;
    background-size: 38px;
}

.greygst{
    background-position: -18px 0px;
    height: 16px;
    width: 16px;
    margin-right: 5px;
    background-size: 36px;
}

.greengst{
    background-position: 0px 0px;
    height: 15px;
    width: 15px;
    margin-right: 3px;
    background-size: 36px;
}