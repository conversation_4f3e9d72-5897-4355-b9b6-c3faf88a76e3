import React, { useEffect, useRef, useState } from 'react';
import { CitySuggester } from './CitySuggester';
import detectmycity from './DetectMyLoc';
import './NEC.css';
import { setCookieREC, reqFormGATrackREC } from '../common/formCommfun';

const CityInput = ({form_param, iso, onChange , onCityChange,val, autoFocus}) => {
    const [localCity, setLocalCity] = useState();
    const [placeholdeer, setPlaceholdeer] = useState('City*');
    const [errcls, setErrcls] = useState('');

    const inputRef = useRef(null);
    useEffect(() => {
        // Focus the input if autoFocus is true
        if (autoFocus && inputRef.current) {
            inputRef.current.focus();
        }
    }, [autoFocus,form_param]); // Re-run effect if autoFocus changes
    const handleDetectCity = async () => {
        reqFormGATrackREC("detectMyCity",form_param);
        try {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    async (position) => {
                        const appsServerName =
                            location.hostname.match(/^dev/)
                                ? "//dev-apps.imimg.com/"
                                : location.hostname.match(/^stg/)
                                ? "//stg-apps.imimg.com/"
                                : "//apps.imimg.com/";

                        const { latitude: lt, longitude: lg, accuracy: accu } = position.coords;

                        const formData = new URLSearchParams();
                        formData.append('token', 'imartenquiryprovider');
                        formData.append('S_lat', lt.toFixed(5));
                        formData.append('S_long', lg.toFixed(5));
                        formData.append('GET_CITY', 'Y');
                        // Assuming `modid` is defined elsewhere
                        formData.append('modid', modid);

                        const url = `${appsServerName}index.php?r=Newreqform/GeoLocation`;

                        const responseData = await detectmycity(formData, url);

                        if (responseData && parseInt(responseData.CODE) === 200) {
                            const { cityname, state, stateid, cityid } = responseData;
                            setLocalCity(cityname);
                            onChange(cityname); // Notify parent component of city change
                            onCityChange({ cityname, statename: state, stateid, ctid: cityid }); // Propagate city details to parent
                            
                            const geolocdecode = `GeoLoc_lt=${lt}|lg=${lg}|accu=${accu}|lg_ct=${cityname}|lg_ctid=${cityid}`;
                            const existingCookie = document.cookie.split(';').find((cookie) => cookie.trim().startsWith('iploc='));
                            
                            if (existingCookie) {
                                // let ipLoc = existingCookie.split('=')[1];
                                let ipLoc = existingCookie.split(/=(.+)/)[1]
                                if (ipLoc.includes('|GeoLoc_lt')) {
                                    // Remove the '|GeoLoc_lt' and everything after it
                                    ipLoc = ipLoc.substring(0, ipLoc.indexOf('|GeoLoc_lt'));
                                }
                                ipLoc += '|' + geolocdecode;
                                setCookieREC('iploc', ipLoc, 3,true); // Update existing iploc cookie
                            } else {
                                setCookieREC('iploc', geolocdecode, 3,true); // Set new iploc cookie
                            }
                        }
                        // else {

                        //     blenqGATracking('geoloc', 'service:GeoLocation:failure', res, 1, ReqObj.UserDetail["ipcityname"].substr(2, 4));

                        // }
                    },
                    (error) => {
                        console.error('Error getting geolocation:', error);
                    },
                    { timeout: 10000 }
                );
            } else {
                console.error('Geolocation is not supported');
            }
        } catch (error) {
            console.error('Error fetching city info:', error);
            imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'Error fetching city info','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
        }
    };
    const handleCityInputChange = (e) => {
        const inputValue = e.target.value;
        setLocalCity(inputValue);
        onChange(inputValue);
        onCityChange({ cityname: inputValue });
        const main_city_sugg = new Suggester({
            element: 'city',
            onSelect: handleCitySugg,
            type: "city",
            fields: "std,state,id,stateid",
            minStringLengthToDisplaySuggestion: 1,
            rowsToDisplay: '5',
            autocompleteClass: "ber-sugg CitySuggestor",
            displayFields: "value,state",
            displaySeparator: " >> ",
            filters: "iso:" + iso,
            recentData: false,
        });
        
    };

    const handleCitySelection = (selectedCity,cityid) => {
        // Update the local state
        setLocalCity(selectedCity);
        // Notify the parent component of the selected city
        onChange(selectedCity);
        onCityChange({ cityname: selectedCity, ctid: cityid });
    };
    const handleCitySugg = (e,v) => {
        onCityChange({ cityname:v.item.value, statename: v.item.data.state, stateid:v.item.data.stateid, ctid: v.item.data.id });
        // Update the local state
        setLocalCity(e.target.value);
        // Notify the parent component of the selected city
        onChange(e.target.value);
    };
    let imgDetect='';
    if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
        imgDetect = "imgDetect";
    }
    useEffect(() => {
        if(localCity && localCity.trim().length!=0){
            setPlaceholdeer('City*');
            setErrcls('');
        }else{
            if(val.citycheck){
                setPlaceholdeer('City*');
                setErrcls('');
            }else{
                setPlaceholdeer('Please Enter Your City*');
                setErrcls('input-error');
                setLocalCity("");
            }
        }
    }, [val.citycheck,localCity]);
    return (
        <div className="form-group pr">
            <input
                ref={inputRef}
                type="text"
                id="city"
                placeholder={placeholdeer}
                className={errcls}
                value={localCity}
                onChange={handleCityInputChange}
                // readOnly={value !== ""}
            />
            <a type="button" onClick={handleDetectCity} className={`detect-button cp ${imgDetect}`}>
                Detect My City
            </a>
            <CitySuggester onSelectCity={handleCitySelection} form_param ={form_param}/>
        </div>
    );
};

export default CityInput;
