import React, { useState, useRef, useEffect } from "react";
import "./User_otp.css";
import { isBlRevampd } from "../common/formCommfun";

export default function OtpInputs({ form_param, onConcatenatedOtp,  shouldFocus, setShouldFocus}) {
  const [otpValues, setOtpValues] = useState({
    bl_enqOtp1: "",
    bl_enqOtp2: "",
    bl_enqOtp3: "",
    bl_enqOtp4: ""
  });

  const refs = {
    bl_enqOtp1: useRef(null),
    bl_enqOtp2: useRef(null),
    bl_enqOtp3: useRef(null),
    bl_enqOtp4: useRef(null)
  };

  useEffect(() => {
    if (shouldFocus) {
      setOtpValues({
        bl_enqOtp1: "",
        bl_enqOtp2: "",
        bl_enqOtp3: "",
        bl_enqOtp4: ""
      });
      if (refs.bl_enqOtp1.current) {
        refs.bl_enqOtp1.current.focus();
      } 
      // setShouldFocus(false);
    }
  }, [shouldFocus,form_param]);

  // useEffect(() => {
    
  //   if (refs.bl_enqOtp1.current) {
  //     refs.bl_enqOtp1.current.focus();
  //   }
  // }, []);

  const handleKeyPress = (event) => {
    if (!isNumberKey(event)) {
      event.preventDefault();
    }
  };

  const handleKeyUp = (event, nextField) => {
    const { keyCode } = event;
    const { value, name } = event.target;

    setOtpValues(prevState => {
      const newOtpValues = { ...prevState, [name]: value };
      const concatenatedOtp = `${newOtpValues.bl_enqOtp1}${newOtpValues.bl_enqOtp2}${newOtpValues.bl_enqOtp3}${newOtpValues.bl_enqOtp4}`;
      onConcatenatedOtp(concatenatedOtp);
      return newOtpValues;
    });

    if (value !== "" && keyCode !== 8) {
      if (refs[nextField] && refs[nextField].current) {
        refs[nextField].current.focus();
      }
    }
  };

  const handleKeyDown = (event, currentField, prevField) => {
    const { keyCode } = event;
    const { value } = event.target;
    if (keyCode === 8) {
      event.preventDefault();
      if (refs[currentField] && refs[currentField].current) {
        refs[currentField].current.value = "";
        if (value === "") {
          if (refs[prevField] && refs[prevField].current) {
            refs[prevField].current.focus();
            refs[prevField].current.value = "";
          }
        }
      }
    }
  };

  const isNumberKey = event => {
    const charCode = event.which || event.keyCode;
    return charCode >= 48 && charCode <= 57;
  };

  const handleChange = (event, fieldName) => {
    const { value } = event.target;
    if (value.length === 1) {
      setOtpValues(prevState => ({
        ...prevState,
        [fieldName]: value
      }));
    }
  };
  let parcls = "m15 blml20 bepr";
  let childcell  = "bedblk bevtCss";
  if(form_param.formType == 'BL'){
    parcls = "beotpR m15 blml20 bepr";
    childcell = "bedblk";
  }

  return (
    <div className={parcls}>
      <div className={childcell}>
      {form_param.formType == 'BL' && !isBlRevampd(form_param) && <i className="blnewfo_sprit blicotp beflt"></i>}
        {Object.keys(otpValues).map((fieldName, index) => (
          <input
            key={index}
            name={fieldName}
            ref={refs[fieldName]}
            type="text"
            autoComplete="off"
            className={form_param.formType == 'BL' && !isBlRevampd(form_param) ? "bloptin-rec"   : "enqoptin-rec" }
            maxLength="1"
            value={otpValues[fieldName]}
            onChange={e => handleChange(e, fieldName)}
            onKeyPress={e => handleKeyPress(e)}
            onKeyUp={e =>
              handleKeyUp(
                e,
                Object.keys(otpValues)[index + 1] || fieldName
              )
            }
            onKeyDown={e =>
              handleKeyDown(
                e,
                fieldName,
                Object.keys(otpValues)[index - 1] || fieldName
              )
            }
          />
        ))}
      </div>
    </div>
  );
}
