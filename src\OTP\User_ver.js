import React, { useState, useEffect } from "react";
import Head_scr from "../common/heading";
import OtpInputs from "./OtpInputs";
import "./User_otp.css";
import OtpHandleUI from "./OtpHandleUI";
import SubmitOTP from "./SubmitOTP";
import { useGlobalState } from '../context/store';
import { Eventtracking, shouldRenderPriceWidget} from '../common/formCommfun';
// import callPostreq from "../callpostreq";

function User_ver({ form_param }) {
    const id = form_param.tempId + form_param.instId;
    const [errordisp, setErrordisp] = useState('');
    const [disable, setDisable] = useState(0);
    const { state, dispatch } = useGlobalState();
    const [shouldFocus, setShouldFocus] = useState(false);
    const [apicall, setApicall] = useState(false);
    const [initialCall, setInitialCall] = useState(true);
    const [myTimeout, setMyTimeout] = useState(null);
    const [otpreq , setOtpreq] = useState(false);
    const [otpEntered,setOtpEntered] = useState('');
    

    useEffect(() => {
        if (apicall) {
            sendOtp(initialCall ? 0 : 1);
        }
    }, [apicall]);

    useEffect(() => { 
        let scrnm = '';
        if(otpreq){
            scrnm = "FillOTP";
        }else{
            scrnm = "GetOTP";
        }
        Eventtracking("DS" + window.screencount + "-"+scrnm , state.prevtrack , form_param , false);
        dispatch({ type: 'currentscreen', payload: { currentscreen: scrnm } });
    },[otpreq])


    const handleSkipSubmit = async (e) => {
        e.preventDefault();
        let scrnm = '';
        if(otpreq){
            scrnm = "-FillOTP";
        }else{
            scrnm = "-GetOTP";
        }
        Eventtracking("SS" + window.screencount + scrnm + "-Skipped" , state.prevtrack , form_param , false);
        window.screencount++;
        dispatch({ type: 'prevtrack', payload: { prevtrack: state.prevtrack+scrnm } });
        if(state.frscr==2 && !state.searchshown){
            dispatch({ type: "frscr", payload: { frscr: 1 } });
         }
        dispatch({ type: 'OTPcon', payload: { OTPcon: false } });
        if(form_param.formType =="BL"){
            dispatch({ type: 'IsqformBL', payload: { IsqformBL: true } });
          }else{
        dispatch({ type: 'Isqform', payload: { Isqform: true } });}
        dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep+1 } });
    };

const clearMyTimeout = () => {
    if (myTimeout) {
        clearTimeout(myTimeout);
        setMyTimeout(null);
    }
};
 
    const sendOtp = async (resend, e) => {
        if (e) e.preventDefault();
        
        setShouldFocus(true);
        if(window.otpcountRes==3){
            setErrordisp('exceeded');
            disableres (1);
        }
        else if(window.otpcountRes > 0 && window.otpcountRes<3 && window.ratelimitstat !== "Access Denied"){
          disableres (1);
          window.otpcountReser+=1;
          //remove disbaled button after 20 sec
          const timeoutId = setTimeout(() => {
            disableres(0); 
        }, 20000);

        setMyTimeout(timeoutId);
        }
        if(window.otpcountRes!=3){
            OtpHandleUI(resend, 1, '', setErrordisp, disableres,'',clearMyTimeout,'',form_param);
        }      
        setApicall(false);  // Reset apicall after calling sendOtp
        setInitialCall(false);  // Mark initial call as done
    };

    const logConcatenatedOtp = (concatenatedOtp) => {
        setOtpEntered(concatenatedOtp);
    };

    const getOTPclicked = () => {
        setApicall(true);
        setOtpreq(true);
        Eventtracking("SS" + window.screencount + "-GetOTP-Requested", state.prevtrack, form_param, false);
        window.screencount++;
    };

    const disableres = (val) => {
        setDisable(val);
    };
    let alignBLOTP = '';
    let nootpp='nootpp';
    let errotp = 'errorOtpR';
    let otpbtnfont = 'otpbtnfont';
    if(form_param.formType == 'BL'){
        alignBLOTP = 'centred';
        nootpp ='nootppbl';
        errotp = 'errorOtpR2';
    }
    let otpAlign='';
    if(form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf'){
        otpAlign = "otpAlign";
        otpbtnfont = 'otpbtnfontimg';
    }
    

    const handleKeyPress = (event) => {
        if (event.keyCode === 13 && !otpreq) {
            getOTPclicked();
        }
    };

    useEffect(() => {
            if (!otpreq) {
                document.addEventListener('keydown', handleKeyPress);
                return () => {
                    document.removeEventListener('keydown', handleKeyPress);
                };
            }
        }, [otpreq]);

    return (
        <div id={`t${id}screen3`} className="bedvh">
            <div id={`t${id}clslog`} className="enqLogIn">
                {!(shouldRenderPriceWidget(form_param) && otpreq) && <Head_scr scr={"otp"} hash={form_param} otpreq={otpreq} />}
                {!otpreq && <div className='outOtp'><div className={`getotpcls ${otpbtnfont}`} onClick={getOTPclicked}>
                    Get OTP
                </div>
                {/* {window.showskipBut? <button className="skipOTPFirst" onClick={handleSkipSubmit}>Skip</button>:''} */}
                </div>}
                {otpreq && <><div className="blotp bemb20">
                    Enter the 4 digit One Time Password (OTP) sent to your Mobile Number via SMS 
                </div>
                <div className={alignBLOTP}>
                    <OtpInputs form_param ={form_param} onConcatenatedOtp={logConcatenatedOtp} shouldFocus={shouldFocus} setShouldFocus={setShouldFocus} />
                    <div id="t0901verifyerrotpdiv" className={errotp} data-role="" >
                        {errordisp === 'resend clicked' ? (
                        <span id="t0901verify_err" >OTP sent on Your Mobile</span>
                        ) : errordisp === 'empty' ? (
                        <span id="t0901verify_err" >Please enter OTP</span>
                        ): errordisp === 'incorrect' ? (
                            <span id="t0901verify_err" >Please enter correct OTP</span>
                        ) : errordisp === 'exceeded' ? (
                            <span id="t0901verify_err" >You have exceeded allowed OTP request attempts</span>
                        ) : errordisp.includes('Too many OTP requests') ? (
                            <span id="t0901verify_err" >{errordisp}</span>
                        ) :('')}
                    </div>
                    <SubmitOTP form_param ={form_param} errordisp={errordisp} setErrordisp={setErrordisp}  setShouldFocus={setShouldFocus} otpEntered={otpEntered} />
                    <div id="noOtp" className={`${nootpp} ${otpAlign}`} style={{ marginTop: '25px' }}>
                        Did not receive OTP?
                        {disable === 1 ?
                            <button id="t0901resendOtp" className="blSmst disabRes" disabled="disabled" >Resend</button> :
                            <button id="t0901resendOtp" className="blSmst" style={{ cursor: 'pointer' }} onClick={() => 
                                {
                                    setApicall(true); 
                                }}>Resend</button>
                        }
                        
                        {/* {window.showskipBut ? <button className="skipOTP" onClick={handleSkipSubmit}>Skip</button>:''} */}
                    </div>
                </div></>}
            </div>
        </div>
    );
}

export default User_ver;
