.screen4 .ber-txt {
    resize: none;
    height: 48px;
    font-size: 15px;
    font-family: arial;
    line-height: 18px;
    padding: 5px 9px;
    margin: 0;
    outline: 0;
    box-sizing: border-box;
}
.screen4 .slbox {
    width: 100%;
    border: 1px solid #c9c6c6;
    border-radius: 2px;
    background-color: #fff;
    color: #000;
    vertical-align:top;
}
.blnewfo_sprit{
  background-image: url(https://apps.imimg.com/gifs/blform-sprite22.png);
  background-repeat: no-repeat;
}
.be-noimgR {
  background-position: -5px -187px;
  height: 177px;
  width: 200px;
  margin: auto;
}
.ber-nobgimg {
  background: #fff;
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
}
.screen4 .custom-radio{
    display: inline-block;
    box-sizing: border-box;
    /* position: relative;
    vertical-align: top; */
    margin: 8px 13px 0px 0;
    background: #fff;
    /* height: 34px; */
    /* line-height: 34px; */
    /* border-radius: 2px; */
    border: 1px solid #c1c1c1;
    /* width: auto; */
}
.custom-radio input[type="radio"] {
    display: none;
  }
.cityinpnone{
display: none;
}
  
  /* Create a custom radio button */
  .custom-radio label {
      position: relative;
      cursor: pointer;
      display: inline-block;
      padding: 8px 10px 8px 30px;
  }
  
  /* The circle for the custom radio button */
  .custom-radio label::before {
      content: '';
      position: absolute;
      left: 7px;
      top: 7px;
      width: 16px;
      height: 16px;
      border: 2px solid #ccc;
      border-radius: 50%;
      background: #fff;
  }
  .custom-radio input[type="radio"]:checked + label{
      background-color: #2f3292;
      color: white;
  }
  /* The dot inside the custom radio button */
  .custom-radio input[type="radio"]:checked + label::after {
      content: '';
      position: absolute;
      left: 13px;
      top: 13px;
      width: 7px;
      height: 7px;
      border-radius: 50%;
      background:#2f3292;
  }
  
  /* Change the border color when hovering */
  .custom-radio label:hover::before {
    border-color: #2f3292;
  }
  .cityinp{
    position: relative;
    margin-bottom: 15px;
    margin-top: 5px;
  }
  .boxht{
    width: 153px;
    margin-right: 8px;
    position: relative;
    display: inline-block;
  }
  .cityinpin{
    height: 34px;
    vertical-align: top;
    padding: 0px 0 0 8px;
    margin: 0px;
    font-size: 15px;
    border: 1px solid #c9c6c6;
    border-radius: 2px;
    background-color: #fff;
    color: #000;
    box-sizing: border-box;
    width: 100%;
  }
  .ber-sugg {
    z-index: 999 !important;
  }
  .subBut{
    width: 180px;
    left: 0px;
    right: 0px;
    display: inline-block;
    margin-top: 25px;
  }
  .form-btn {
    background: #00a699;
    border: none;
    border-radius: 3px;
    font-weight: 700;
    margin: 0;
    height: 48px;
    line-height: 47px;
    margin-bottom: 0;
    color: #fff !important;
    width: 180px;
    font-size: 16px;
    font-family: arial !important;
    display: block;
    cursor: pointer;
}
.ber-RscBL{
  background: #fff;
    position: relative;
    padding: 25px 20px 15px 20px;
    line-height: 18px;
    flex: auto;
    font-size: 14px;
}

.boxsizeInact{
  box-sizing: initial!important;
}
.ber-LscHeight {
  background-color: transparent;
  color: black;
}
.ber-LscHeight .ber-prdimg {
  border: 1px solid #e4e4e4;
}

.ber-LscHeight {
  min-height: 300px;
  padding: 10px 10px 10px;
}

.marBTM{
  margin-bottom: 15px;
}

.blMarfn{
  margin-top: 13px;
  font-size: 16px!important;
}

@media only screen and (min-width: 1515px) {
  .blHW .ber-LscBL {
    min-height: 600px!important;
  }

  .blHW .ber-LscBL.ber-LscHeight{
    min-height: 300px!important;
  }

  .blHW{
    min-height: 500px!important;
  }
}

.blNew .br24L{
  border-radius: 24px 0 0 24px;
}
.blNew .br24{
  border-radius: 24px;
}
.blNew .br24R{
  border-radius: 0 24px 24px 0;
}
.blNew .ber-LscBL{
  width: 45%;
  background: #e4f5f3;
  padding-top: 132px;
}
.inrDv {
    width: 95%;
    background-color: white;
    border-radius: 24px;
    margin: 0 auto 15px;
    position: relative;
    padding-top: 120px;
    top: 50%;
    transform: translateY(-50%);
}
.headBl{
  color: #007A6E;
    margin: auto;
    font-size: 20px;
}
.divhd{
     padding: 30px;
    padding-bottom: 5px;
    text-align: center;
    line-height: 22px;
    bottom: 0;
}
.inHead{
  color: #323232;
  font-size: 15px !important;

}

.imgDivs{
    background-color: white;
    width: 100px;
    height: 100px;
   
    border-radius: 8px;
    border: 5px;
    top: 100px;
     text-align: center;
     padding: 5px;
     box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);


}

.imgDivs img,.imgDivs .imgInside{
     width: 100%;
    object-fit: cover;
    height: 100%;
    border-radius: 5px;
}

.imgDivs.boxsNn{
  box-shadow: unset ;
}

.imgDivs.exclDiv{
     height: 200px;
    width: 200px;
    border-radius: 16px;
    top: -100px;
    position: absolute;
    left: 25%;
     box-shadow: 0px 20px 25px 0px rgba(35, 35, 35, 0.15), 0px 8px 10px 0px rgba(35, 35, 35, 0.15);
}

.imgConDiv{
     gap: 20px;
    width: 100%;
    top: 115px;
    justify-content: center;
    align-items: center;
}
.blNew .blpopgoin,.blNew .submit-button,.blNew .subBut .form-btn,.blNew .otpbtnfont,.blNew .blotpbtn,.blNew .mdSub{
      width: 400px !important;
    height: 49px!important;
    text-align: center;
    background-position: 62% 50%;
    border-radius: 8px!important;
    background-color: #007A6E!important;
}

.blNew .porlt,.blNew .form-container,.blNew .bepdpreq{
  width: 400px;
}
.blNew .contact-form,.blNew .input-container,.blNew .cnamecont,.blNew .eqflot,.blNew .subBut .form-btn,.blNew .subBut{
  width: 100% !important;
}

.blNew .otpbtnfont,.blNew .blotpbtn{
   width: 300px!important;
    height: 47px!important;
}
.blNew .blpopgo{
      top: 20px;
    width: 100%;
    left: unset;
    position: relative;
}

  .blNew .slbox.ber-txt,  .blNew .be-slbox,.blNew .grCBl,.blNew .oEq_r .enqLogIn .ber-input,.blNew .form-group input[type="text"],.blNew .form-group input[type="email"],.blNew input[type="text"][name="phoneNumber"],.blNew  #mdDiv input[type="text"],.blNew .loginName{
    background-color: #F2FAF9!important;
    height: 50px !important;
    width: 100% !important;
    border-radius: 8px !important;

  }
  .blNew .enqoptin-rec,.blNew .cdiso input[type="text"] {
 background-color: #F2FAF9!important;
 border-radius: 8px !important;
  }
  .blNew .oEq_r .enqLogIn .logDv .ber-input{
    padding-left: 10px!important;
  }
  .blNew .cdiso input[type="text"] {
    border: 1px solid #c9c6c6;
    height: 50px;
  }

  .blNew .logDv{
    display: flex;
    gap: 5px;

  }


  .blNew .blotp,.blNew .oEq_r .ber-hdg-r,.blNew .form-group,.blNew .form-container .eqs16,.blNew #mdDiv .eqs16,.blNew .nonImg{
    text-align: center;
    padding-right: unset;
  }

  .blNew .be-slbox{
    height: 50px;
  }

  .blNew .outOtp{
    padding: 5px 50px;
  }

  .blNew .alignTp{
    margin-top: 30px !important;
  }

  .blNew .ber-RscBL{
        display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .blNew .detect-button{
    right: 0px;
    top: 55px;
  }

   .chkBox.selRd .beisq3,.chkBox.selRd{
    color: #029f93!important ;
    border-color: #029f93!important;
  }
  .beradio-sl{
    background: #029f93 !important;
  }
  .chkBox.selRd .bechkin{
    border: 1px solid #029f93 !important;
  }
  