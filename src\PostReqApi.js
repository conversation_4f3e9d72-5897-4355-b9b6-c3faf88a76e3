import { isset, readCookieREC, setCookieREC, getparamValREC, reqFormGATrackREC, imeqglval, getloginmode, MakeRefTextRec,callbacktoProp} from './common/formCommfun';
export default async function PostReq(form_param,paramobjpostreq) {
// function MakeRefTextRec() {
//     const form = form_param;
//     if (!form) return "";
//     const {
//       ctaName,
//       ctaType,
//       ct,
//       MP,
//       ex,
//       imgf,
//       isqf,
//       cmconv,
//       isDist,
//       shownMultiImg,
//       pageType,
//       section,
//       position,
//       scriptVersion,
//       compRank,
//       FCPRank,
//       mcatName,
//       sllrRtng,
//       isCityID,
//       modId,
//     } = form;
  
//     let ctan = isset(() => ctaName) ? ctaName : "";
//     let ctaT = isset(() => ctaType) ? ctaType : "";
//     let ct_1 = isset(() => ct) ? ct : "";
//     let mp_1 = isset(() => MP) ? MP : "";
//     let ex_1 = isset(() => ex) ? ex : "";
//     let img_f = isset(() => imgf) ? imgf : "";
//     let isq_f = isset(() => isqf) ? isqf : "";
//     let cm_1 = isset(() => cmconv) ? cmconv : "";
//     let isDistValue = isset(() => isDist) ? isDist : "";
  
//     if (isset(() =>shownMultiImg) && shownMultiImg === 1 && isset(() =>ctaName)) {
//       if (ctaName.toLowerCase() === "image") {
//         ctan = "Image-M";
//       } else if (ctaName.toLowerCase() === "image_next") {
//         ctan = "Image-M_Next";
//       } else if (ctaName.toLowerCase() === "image_pre") {
//         ctan = "Image-M_Pre";
//       }
//     }
  
//     let refText = `ctaName=${ctan}|ctaType=${ctaT}|PT=${pageType}`;
  
//     if (modId === "DIR" && isset(() =>ct_1) && isset(() =>pageType) && pageType.includes("city-mcat")) {
//       refText += `|${ct_1}`;
//     }
//     if (isset(() =>mp_1) && mp_1 !== "") {
//       refText += `|${mp_1}`;
//     }
//     if (isset(() =>ex_1) && ex_1 !== "") {
//       refText += `|ex=${ex_1}`;
//     }
//     if (isset(() =>cm_1) && cm_1 !== "") {
//       refText += `|${cm_1}`;
//     }
//     if (isset(() =>img_f) && img_f !== "") {
//       refText += `|${img_f}`;
//     }
//     if (isset(() =>isq_f) && isq_f !== "") {
//       refText += `|${isq_f}`;
//     }
  
//     refText += `|Section=${isset(() =>section) ? section : ""}`;
//     refText += `|Position=${isset(() =>position) ? position : ""}`;
//     refText += `|ScriptVer=${isset(() =>scriptVersion) ? scriptVersion : ""}`;
//     refText += `|compRank=${(isset(() =>compRank) && modId === "DIR") ? compRank : (isset(() =>FCPRank) ? FCPRank : "")}`;
//     refText += `|searchTerm=${isset(() =>mcatName) ? mcatName : ""}`;
  
//     if (isset(() =>isCityID) && isCityID !== "") {
//       refText += `|is_dist=${isDistValue}`;
//     }
//     if (isset(() =>form_param.appendQRef) && form_param.appendQRef !== "") {
//       refText += `|=${form_param.appendQRef}`;
//     }
    
  
//     const sllr_rating = (isset(() =>sllrRtng) && sllrRtng !== "") ? sllrRtng : 0;
//     if (modId === "DIR") {
//       refText += `|slr=${sllr_rating}`;
//     }
      
//     return refText;
//   }
  const imesh = isset(() => readCookieREC('ImeshVisitor')) ? readCookieREC('ImeshVisitor') : '';
  const name = getparamValREC(imesh, 'fn') || "";
  const glid = getparamValREC(imesh, 'glid');
  const imissCookie = isset(() => readCookieREC('im_iss')) ? readCookieREC('im_iss') : '';
  const referrer = isset(() => readCookieREC('site-entry-page')) ? readCookieREC('site-entry-page') : '';
  
  let isDist = isset(() =>(form_param.isDist)) ? (form_param.isDist) : "" ;
  let currUrl = window.location.href;
  if(isset(() =>(form_param.formType)) && (form_param.formType.toLowerCase() == "bl" )){
    if( isset(() =>(form_param.ct)) && form_param.ct !== "")
    currUrl = (isset(() =>(currUrl)) && currUrl!== "") ? currUrl.endsWith(".html") ? currUrl + '?' + form_param.ct  : currUrl + '&' + form_param.ct : "''" ;
    if( isset(() =>(form_param.imgf)) && form_param.imgf !== "")
    currUrl = (isset(() =>(currUrl)) && currUrl!== "") ? currUrl.endsWith(".html") ? currUrl + '?' + form_param.imgf  : currUrl + '&' + form_param.imgf : "''" ;
    if( isset(() =>(form_param.isqf)) && form_param.isqf !== "")
    currUrl = (isset(() =>(currUrl)) && currUrl!== "") ? currUrl.endsWith(".html") ? currUrl + '?' + form_param.isqf  : currUrl + '&' + form_param.isqf : "''" ;
    if( isset(() =>(form_param.Abexp)) && form_param.Abexp !== "")
    currUrl = (isset(() =>(currUrl) )&& currUrl!== "") ? currUrl.endsWith(".html") ? currUrl + '?' + form_param.Abexp  : currUrl + '&' + form_param.Abexp : "''" ;
    if(isset(() =>(form_param.ex)) && form_param.ex !== "")
    currUrl = (isset(() =>(currUrl)) && currUrl!== "") ? currUrl.endsWith(".html") ? currUrl + '?' + form_param.ex  : currUrl + '&' + form_param.ex : "''" ; 
    if(isset(() =>(form_param.isCityID)) && form_param.isCityID !== ""){
      currUrl = (isset(() =>(currUrl) && currUrl!== ""))  ? currUrl.endsWith(".html") ? currUrl + '?is_dist' + isDist : currUrl + '&is_dist' + isDist : "''" ;
    }
  } 
  if( isset(() =>(form_param.pdpTemplate)) && form_param.pdpTemplate !== ""){
    currUrl = (isset(() =>(currUrl) && currUrl!== ""))  ? currUrl.endsWith(".html") ? currUrl + '?tempid=' + form_param.pdpTemplate : currUrl + '&tempid=' + form_param.pdpTemplate : "''" ;
  }
  if(isset(() =>(form_param.formType)) && (form_param.formType.toLowerCase() == "bl" )){
    currUrl = (isset(() =>(currUrl) && currUrl!== ""))  ? currUrl.endsWith(".html") ? currUrl + '?form_react' : currUrl + '&form_react' : "''" ;
    if(MakeRefTextRec(form_param)){
      currUrl = (isset(() =>(currUrl) && currUrl!== ""))  ? currUrl.endsWith(".html") ? currUrl + '?' + MakeRefTextRec(form_param) : currUrl + '&' + MakeRefTextRec(form_param) : "''" ;
    }
  }

  const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
  const s_ip = getparamValREC(iploc, 'gip');
  const s_ip_country = getparamValREC(iploc, 'gcnnm');
  const s_country_iso = getparamValREC(iploc, 'gcniso');
  const mobile = getparamValREC(imesh, 'mb1') || "";
  let formData = {
    s_prod_name: form_param.prodName,
    s_name: paramobjpostreq.name &&  paramobjpostreq.name != '1' ? paramobjpostreq.name : name,
    s_first_name: paramobjpostreq.name &&  paramobjpostreq.name != '1' ? paramobjpostreq.name : name,
    flag: form_param.formType,
    afflid: form_param.afflId,
    s_city_id: paramobjpostreq.s_city_id && paramobjpostreq.s_city_id != "1"  && paramobjpostreq.s_city_id != "0" ? paramobjpostreq.s_city_id : "",
    category_type: 'p',
    modref_id:form_param.pDispId,
    modid: form_param.modId,
    mcatID: form_param.mcatId,
    catID: form_param.catId,
    login_mode: window.LoginmodeReact || getloginmode(),
    s_glusrid: glid,
    s_country_iso: window.countryiso || s_country_iso,
    s_ip_country: s_ip_country,
    s_ip: s_ip,
    s_ip_country_iso: s_country_iso,
    curr_page_url: currUrl,
    landing_ref_url: referrer,
    s_country_name: window.country || s_ip_country,
    rfq_query_ref_text: MakeRefTextRec(form_param),
    prev_page_url : document.referrer,
}
if(form_param.formType == 'Enq'){
  formData['r_glusrid']= form_param.rcvGlid;
  formData['modref_type']=form_param.modrefType;
  formData['s_prod_dispname']=form_param.prodDispName || form_param.prodName;
  formData['pdp']=form_param.modId === "PRODDTL" ? true : false;
}else if(form_param.formType == 'BL'){
  formData['s_mobile']=mobile;
}
if(isset(() => form_param.prodServ) && form_param.prodServ !== ''){
  formData['prod_serv']=form_param.prodServ;
}
formData = new URLSearchParams(formData);

    try {
        let webAddressLocation = location.hostname;
        const appsServerName = webAddressLocation.match(/^dev/)
        ? "//dev-apps.imimg.com/"
        : webAddressLocation.match(/^stg/)
          ? "//stg-apps.imimg.com/"
          : "//apps.imimg.com/";
        const url = appsServerName + "index.php?r=Newreqform/Postreq";
        const response = await fetch(url, {
          method: 'POST',
          mode: 'cors', // Specify 'cors' mode to allow cross-origin requests
          body: formData // Pass form data as the request body
        });
  
      if (!response.ok) {
        reqFormGATrackREC("service:Enquiry generation:failure",form_param);
        throw new Error('Failed to call API');
      }
  
      const responseData = await response.json();
      reqFormGATrackREC("service:Enquiry generation:success ",form_param);
      if(form_param.formType == 'Enq'){
        window.rfq_queryDestinationRec = responseData.query_destination;
        window.queryid = responseData.queryid;
        form_param.generationId=responseData.queryid;
        callbacktoProp(form_param);
      }
      else if(form_param.formType == 'BL'){
        window.queryid = responseData.ofr;
        responseData.queryid = responseData.ofr;
      }

      const imeqarr = imeqglval();

      if(!imeqarr.BL && form_param.formType == 'BL'){
        window.showskipBut=true;
        //create cookie
        let value = "BL" + responseData.usr_id;
        if(imeqarr.eqglval){
          value = imeqarr.eqglval+","+value;
        }
        setCookieREC("imEqGl", value, "1", true);  
      }else if(!imeqarr.Enq && form_param.formType != 'BL'){
        window.showskipBut=true;
        //create cookie
        let value = isset(()=>form_param.enquiryDivID) && form_param.enquiryDivID !== "" ? form_param.enquiryDivID : "dispid" + form_param.pDispId;
        if(imeqarr.eqglval){
          value = imeqarr.eqglval+","+value;
        }
        setCookieREC("imEqGl", value, "1", true);
      }
      else{//cookie present
       
        window.showskipBut=false;
        
      }
      if(form_param.isSoftwareTemp == 1 && form_param.downloadLink != "") {
        window.open(form_param.downloadLink, "_blank");
        window.closeform = true;
      }
      
      return responseData;
      
    //   setApiResponse(responseData);
    } catch (error) {
      reqFormGATrackREC("service:Enquiry generation:failure",form_param);
      console.error('Failed to call API:', error);
      imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'postreqFailure','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
  }