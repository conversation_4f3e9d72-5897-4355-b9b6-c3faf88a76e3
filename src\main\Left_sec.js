import React from 'react';
import WidgetRatings from '../Thankyou/WidgetRatings';
import ResponseRate from '../Thankyou/ResponseRate';
import MembersSince from '../Thankyou/MembersSince';
import GstShow from '../Thankyou/GstShow';
import SupplierType from '../Thankyou/SupplierType';
import PrevNext from '../image_enq/PrevNext';
import { useGlobalState } from '../context/store';
import { reqFormGATrackREC, safeDecodeURIComponent, shouldRenderPriceWidget } from '../common/formCommfun';
function Left_sec({ form_param, id }) {
    const { state, dispatch } = useGlobalState();
    // let soldby_txt = [form_param.rcvName, form_param.rcvCity, form_param.rcvState].filter(val => val && val.trim() !== "").join(", ");
    let isq_arr = safeDecodeURIComponent(form_param.plsqArr).split('#');
    let price = form_param.price;
    let pr = "", ut = "";
    if (price) {
        // Remove currency symbols and unnecessary text
        price = price.replace(/₹|Approx.*?Rs|Rs/g, "").trim();

        // Extract price and unit safely
        [pr, ut] = price.includes("/") ? price.split("/").map(item => item.trim()) : [price, ""];

        // Format price with Rupee symbol (HTML encoded) and append "/" only if unit exists
        pr = pr ? `₹ ${pr}${ut ? "/" : ""}` : `₹ ${ut}`;
    }
    let icon = <i></i>;
    let dispType = '';
    if (form_param.catalog_url) {
        if (form_param.verified_exporter_flag == 1) {
            icon = <i className="sellericons oef0 veSlr" width="14" height="14"></i>;
            dispType = 'Verified Exporter';
        } //verified exporter
        else if (form_param.custtype == 149 || form_param.custtype == 179 || form_param.ts_code) {
            icon = <i className="sellericons oef0 tvfSlr" width="14" height="14"></i>;
            dispType = 'TrustSEAL Verified';
        } //trustseal
        else if ((form_param.custtype <= 699)) {
            icon = <i className="sellericons oef0 vpsSlr" width="14" height="14"></i>
            dispType = 'Verified Plus Supplier';
        } //verified plus
        else if ((form_param.custtype > 699) && (form_param.custtype <= 1899) && form_param.free_supplier_verified_flag == 1) {
            icon = <i className="sellericons oef0 vsSlr" width="14" height="14"></i>;
            dispType = 'Verified Supplier';
        } //verified
        else { icon = <i></i> }
    }
    return (
        <>
            <div id={`t${id}_leftS`} className="ber-Lsc nwInn">
                <div className="belodrbg dispNone" id={`t${id}_belodrVid`}>
                    <div className="blLoadR"></div>
                </div>
                <div id={`t${id}_leftsection`}>
                    <div id={`t${id}_prodimgR`} className="ber-prdimg" data-role="" >
                        <img id={`t${id}_zoomimage`} src={form_param.displayImage} alt=""></img>
                        {state.nextprev !== 0 && <PrevNext form_param={form_param} />}
                    </div>
                    <div id={`t${id}_proddetails`} className="bepr lTxt " data-role="" >
                        <div className="pr mt20">
                            <div id={`t${id}_Prodname0L`} className="ber-pnm eprod" data-role="" >{(form_param.prodDispName || form_param.prodName)}</div>
                        </div>
                        {price && !shouldRenderPriceWidget(form_param) && !state.priceWidgetApiResponse && <div id={`t${id}_ProdPrice0L`} className="eqprodpr" data-role="" >
                            <span id={`t${id}_price0L`} className="eqpr">{pr}</span>
                            <span id="t0901_unit0L">{ut}</span>
                        </div>}
                        <div id={`t${id}_Compname0L`} className="befs16" ></div>
                        <div id={`t${id}_soldBy0L`} className="eqsoldby" >
                            <span id={`t${id}_sold0L`} className="eqsold">Sold By - </span>
                            {form_param.redirectUrl && form_param.redirectUrl.cmpUrl ?
                            <a id={`t${id}_addr0L`} className='compurlredir compurlr' href={form_param.redirectUrl.cmpUrl} target='_blank' onClick={() => reqFormGATrackREC("Company URL Clicked", form_param)}>
                            <svg width="16" height="16" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '2px', verticalAlign: 'middle', marginTop: '-2px' }}
                            ><path d="M1.58333 0C0.717992 0 0 0.717992 0 1.58333V12.6667C0 13.532 0.717992 14.25 1.58333 14.25H12.6667C13.532 14.25 14.25 13.532 14.25 12.6667V7.125H12.6667V12.6667H1.58333V1.58333H7.125V0H1.58333ZM8.70833 0V1.58333H11.5472L4.19027 8.94027L5.30973 10.0597L12.6667 2.7028V5.54167H14.25V0H8.70833Z" fill="#2E3192"></path></svg>{form_param.rcvName}</a>:
                            <span id={`t${id}_addr0L`} className='compurlr'>{form_param.rcvName}</span>}
                        </div>
                        {((form_param.ctaName == 'Click To Call' || form_param.ctaName == 'view mob e') && form_param.pnsNumber) && <div id={`t${id}_pnsEnq0L`} className="befs16">
                            <span className="pnsEnq">
                                <span id="pnsnoenq" className="pnsno">{form_param.pnsNumber}</span>
                            </span>
                        </div>}
                        {/* {isq_arr[0] != "" && <div id={`t${id}_isqdetails0L`}  >
                            {isq_arr.map((item, index) => {
                                if (index < 3) {
                                    const isqkey = item.split(':');
                                    if (safeDecodeURIComponent(isqkey[0]) != 'Quantity' && safeDecodeURIComponent(isqkey[0]) != "Quantity Unit") {
                                        return (
                                            <div key={`t0901_isqkey${index}`} className="lhSldby">
                                                <div className={`flex-container idsf ${index != 2? "isqbtm" :""}`} style={{ flexDirection: 'column' }}>
                                                    <div className="flex-row" style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                                                        <div id={`t0901_isqkey${index}_0`} className="col77 eqElps eqElps1" >
                                                            {safeDecodeURIComponent(isqkey[0]) + " : "}
                                                        </div>
                                                        <div id={`t0901_isqkey${index}_1`} className="eqElps eqElps1 isqvalcls">
                                                            {safeDecodeURIComponent(isqkey[1]).replace(/##/g, ', ')}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        );

                                    }
                                }
                                return null; // Return null for items beyond index 4
                            })}
                        </div>} */}
                        {form_param.rcvCity ? <div className="txtElp txtElp1 bemrg2 clrgry" style={{ display: 'flex' }}>
                            <i className="sellericons oef0 cityPoint" width="14" height="14"></i>{form_param.rcvCity}
                        </div> : <div></div>}
                        {form_param.gst_verified_flag || dispType ? <div className="txtElp txtElp1 bemrg2 cnmAdv">
                            <div className="befs13 idsf mt5 flxwrp id_aic">
                                {form_param.gst_verified_flag ? <GstShow gstVerifiedFlag={form_param.gst_verified_flag} /> : ''}
                                {dispType ? <SupplierType dispType={dispType} icon={icon} /> : ''}
                            </div>
                        </div> : <div></div>}
                        {form_param.membersince || form_param.seller_rating ?
                            <div className="befs13 idsf mt5 flxwrp">
                                {form_param.membersince ? <MembersSince memberSinceDisplay={form_param.membersince} /> : ''}
                                {form_param.seller_rating && form_param.rating_count ? <WidgetRatings rating_count={form_param.rating_count} supplier_rating={form_param.seller_rating} /> : <div></div>}
                            </div> : ''}
                        {form_param.pns_success_ratio ? <ResponseRate pns_success_ratio={form_param.pns_success_ratio} /> : <div></div>}
                    </div>
                </div>
            </div>
        </>

    )

}
export default Left_sec;

