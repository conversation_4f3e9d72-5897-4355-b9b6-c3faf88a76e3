import React, { useState, useEffect } from 'react';
import { useGlobalState } from '../../context/store';
import { readCookieREC, shouldRenderPriceWidget } from '../../common/formCommfun';
import fetchPriceData from './PriceAPI';
import './PriceWidget.css';

// Component for non-authenticated users - no sensitive data in DOM
const LoginPromptWidget = () => {
  return (
    <div className="price-widget">
      <div className="price-widget-header">
        <span>Best Price Available!</span>
      </div>
      <div className="price-widget-content">
        <div className="login-prompt">
          <div className="locked-icon"><svg class="svg-inline--fa fa-lock" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="lock" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="#f97316" d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"></path></svg></div>
          <div className="prompt-text">
            <div className="main-message">Login to Know the Market Price</div>
            <div className="disclaimer">This price will help you in negotiation with seller</div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Component for authenticated users - only loads when user is logged in
const AuthenticatedPriceWidget = ({ form_param }) => {
  const { dispatch } = useGlobalState();
  const [lowestPricedProduct, setLowestPricedProduct] = useState(null);
  const [allProducts, setAllProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [shouldRender, setShouldRender] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [startFadeOut, setStartFadeOut] = useState(false);

  useEffect(() => {
    const loadPriceData = async () => {
      try {
        setLoading(true);

        const result = await fetchPriceData(form_param);

        if (result.success && result.data.products && result.data.products.length > 0) {
          // Store all products for price comparison
          setAllProducts(result.data.products);

          // Find the lowest priced product
          const lowest = result.data.products.reduce((min, product) =>
            product.price < min.price ? product : min
          );

          // Start fade-out animation immediately when API responds
          setStartFadeOut(true);

          // Wait for fade-out animation to complete, then show content
          setTimeout(() => {
            setLowestPricedProduct(lowest);
            setShouldRender(true);

            // Dispatch successful API response to global state
            dispatch({
              type: "priceWidgetApiResponse",
              payload: { priceWidgetApiResponse: true }
            });
          }, 800); // Wait for fade-out animation to complete
        } else {
          // No results or API failure - start fade-out immediately
          setStartFadeOut(true);

          // Wait for fade-out, then hide widget
          setTimeout(() => {
            setShouldRender(false);
            setLoading(false);

            // Dispatch failed API response to global state
            dispatch({
              type: "priceWidgetApiResponse",
              payload: { priceWidgetApiResponse: false }
            });
          }, 800);
        }
      } catch (err) {
        console.error('Price widget error:', err);
        // API error - start fade-out immediately
        setStartFadeOut(true);

        // Wait for fade-out, then hide widget
        setTimeout(() => {
          setShouldRender(false);
          setLoading(false);

          // Dispatch failed API response to global state
          dispatch({
            type: "priceWidgetApiResponse",
            payload: { priceWidgetApiResponse: false }
          });
        }, 800);
      }

      // Set loading to false after successful transition
      setTimeout(() => {
        setLoading(false);
      }, 1600); // 800ms for fade-out + 800ms for content fade-in
    };

    loadPriceData();
  }, [form_param]);

  // Don't render widget if no data available
  if (!shouldRender) {
    return null;
  }

  // No longer needed - using PRICE_F directly from API
  // const formatPrice = (price) => {
  //   if (price === 0) return 'N/A';
  //   return new Intl.NumberFormat('en-IN', {
  //     style: 'currency',
  //     currency: 'INR',
  //     minimumFractionDigits: 0,
  //     maximumFractionDigits: 2
  //   }).format(price);
  // };

  const handleImageError = (e) => {
    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxNkwyMCAyNEwyOCAxNlYyOEgxMlYxNloiIGZpbGw9IiNEREREREQiLz4KPC9zdmc+';
  };

  // Extract numeric values from price strings
  const extractPrice = (priceStr) => {
    const numericValue = priceStr.toString().replace(/[^\d.]/g, '');
    return parseFloat(numericValue);
  };

  // Calculate percentage difference between main product price and widget price
  const calculatePercentageLess = (mainPrice, widgetPrice) => {
    if (!mainPrice || !widgetPrice) return null;

    const mainPriceNum = extractPrice(mainPrice);
    const widgetPriceNum = extractPrice(widgetPrice);

    if (mainPriceNum && widgetPriceNum && mainPriceNum > widgetPriceNum) {
      const percentage = ((mainPriceNum - widgetPriceNum) / mainPriceNum * 100).toFixed(0);
      return percentage;
    }

    return null;
  };

  // Calculate price comparison data for gradient bar
  const calculatePriceComparison = () => {
    if (!allProducts || allProducts.length === 0) return null;

    const mainPrice = form_param.price;
    const mainPriceNum = extractPrice(mainPrice);

    if (!mainPriceNum) return null;

    // Filter products with same unit as main product
    const inputUnit = form_param.price ? form_param.price.toString().split('/').pop()?.trim().toLowerCase() : '';

    let sameUnitProducts = allProducts;
    if (inputUnit) {
      sameUnitProducts = allProducts.filter(product => {
        if (!product.priceDisplay) return false;
        const parts = product.priceDisplay.split('/');
        const productUnit = parts.length > 1 ? parts.pop()?.trim().toLowerCase() : '';
        return productUnit === inputUnit;
      });
    }

    if (sameUnitProducts.length === 0) return null;

    // Calculate prices
    const prices = sameUnitProducts.map(product => extractPrice(product.priceDisplay)).filter(price => price > 0);
    prices.push(mainPriceNum); // Add main product price

    const lowestPrice = Math.min(...prices);
    const highestPrice = Math.max(...prices);
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

    // Calculate positions on the gradient bar (0-100%)
    // 0% = left (red/highest), 100% = right (green/lowest)
    const calculatePosition = (price) => {
      if (highestPrice === lowestPrice) return 50; // If all prices are same, center it
      return ((highestPrice - price) / (highestPrice - lowestPrice)) * 100;
    };

    return {
      currentPrice: mainPriceNum,
      lowestPrice,
      averagePrice,
      highestPrice,
      currentPosition: calculatePosition(mainPriceNum),
      lowestPosition: calculatePosition(lowestPrice),
      averagePosition: calculatePosition(averagePrice),
      sameUnitProductsCount: sameUnitProducts.length
    };
  };

  const priceComparison = calculatePriceComparison();

  if (loading) {
    return (
      <div className="price-widget">
        <div className="price-widget-header">
          <span>Best Price Available!</span>
        </div>
        <div className="price-widget-container">
          <div className={`price-widget-loading ${startFadeOut ? 'fade-out' : ''}`}>
            <span className="loading-text">Unlocking best prices...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="price-widget">
      <div className="price-widget-header">
        <span>Best Price Available</span>
      </div>
      <div className="price-widget-container">
        <div className="price-widget-content">
        {lowestPricedProduct && (
          <div className="lowest-price-product">
            <div className="product-price-container">
              <div className="main-product-price">
                {(() => {
                  const price = form_param.price;
                  if (!price) return 'N/A';

                  const parts = price.toString().split('/');
                  const priceValue = parts[0]?.trim();
                  const unit = parts.length > 1 ? parts[1]?.trim() : '';

                  return (
                    <>
                      <span className="price-value-red">{priceValue}</span>
                      {unit && <span className="price-unit-grey">/{unit}</span>}
                    </>
                  );
                })()}
              </div>
              <div className="current-price-label">Current Price</div>
            </div>
            <div className="product-details">
              <div className="product-price">
                {(() => {
                  const price = lowestPricedProduct.priceDisplay;
                  if (!price) return 'N/A';

                  const parts = price.toString().split('/');
                  const priceValue = parts[0]?.trim();
                  const unit = parts.length > 1 ? parts[1]?.trim() : '';

                  return (
                    <>
                      <span className="price-value">{priceValue}</span>
                      {unit && <span className="price-unit">/{unit}</span>}
                    </>
                  );
                })()}
                {(() => {
                  const mainPrice = form_param.price;
                  const widgetPrice = lowestPricedProduct.priceDisplay;
                  const percentageLess = calculatePercentageLess(mainPrice, widgetPrice);

                  return percentageLess ? (
                    <div className="price-comparison">
                      <span className="percentage-less">
                        <span className="down-arrow">↓</span>
                        {percentageLess}% less
                      </span>
                    </div>
                  ) : null;
                })()}
              </div>
              <div className="product-name">Lowest Price</div>
            </div>
          </div>
        )}

        {/* Price Comparison Bar */}
        {priceComparison && (
          <div className="price-comparison-bar">
            <div className="price-bar-container">
              {/* Current Product Marker */}
              <div
                className="price-marker-dot current-product-dot"
                style={{ left: `${priceComparison.currentPosition}%` }}
              >
                <div className="price-tooltip">
                  ₹{priceComparison.currentPrice.toLocaleString('en-IN')}
                </div>
              </div>

              {/* Average Price Marker */}
              <div
                className="price-marker-dot average-product-dot"
                style={{ left: `${priceComparison.averagePosition}%` }}
              >
                <div className="price-tooltip">
                  ₹{Math.round(priceComparison.averagePrice).toLocaleString('en-IN')}
                </div>
              </div>

              {/* Lowest Price Marker */}
              <div
                className="price-marker-dot lowest-product-dot"
                style={{ left: `${priceComparison.lowestPosition}%` }}
              >
                <div className="price-tooltip">
                  ₹{priceComparison.lowestPrice.toLocaleString('en-IN')}
                </div>
              </div>
            </div>

            <div className="price-markers">
              <div className="price-marker">
                <div className="price-marker-label current-product-label">
                  Current Product
                </div>
              </div>

              <div className="price-marker">
                <div className="price-marker-label average-product-label">
                  Average Price
                </div>
              </div>

              <div className="price-marker">
                <div className="price-marker-label lowest-product-label">
                  Lowest Price
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

// Main component with secure conditional rendering
const PriceWidget = ({ form_param }) => {
  // Check if widget should be rendered based on conditions
  if (!shouldRenderPriceWidget(form_param)) {
    return null; // Don't render widget if conditions not met
  }

  const { state } = useGlobalState();

  // Check if user is logged in
  const isLoggedIn = state.Imeshform && readCookieREC("ImeshVisitor");

  // Early return for non-authenticated users - no sensitive data in DOM
  if (!isLoggedIn) {
    return <LoginPromptWidget />;
  }

  // Only render authenticated content for logged-in users
  return <AuthenticatedPriceWidget form_param={form_param} />;
};

export default PriceWidget;
