import React, { useState, useEffect } from 'react';
import './NEC.css';
import NameInput from './NameInput';
import EmailInput from './EmailInput';
import CityInput from './CityInput';
import SubmitButton from './SubmitButton';
import Head_scr from '../common/heading';
import PhoneInput from './PhoneInput';
import { Eventtracking, isBlRevampd } from '../common/formCommfun';
import { useGlobalState } from '../context/store';

function Contactdtl({ fn, em, mb, ctid, iso, phext, form_param, MoreReq, gst, cname, wUrl, Blpopup , focusfield , blsearchval ,seterr_msg}) {
    const { state, dispatch } = useGlobalState();
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [phone, setPhone] = useState('');
    const [city, setCity] = useState('');
    const [warning, setWarning] = useState('');
    const [checks, setChecks] = useState({
        namecheck: true,
        citycheck: true,
        emailcheck: true
    });
    const [userDetails, setUserDetails] = useState({
        statename: '',
        stateid: '',
        ctid: '',
        cityname: ''
    });
        const [isCityFromSuggester, setIsCityFromSuggester] = useState(true);
        const [validCity, setValidCity] = useState(true);

    


    useEffect(() => {
        let eventcat = "";
        let emailcon = false;
        if (!MoreReq) {
            if (state.newEnq && state.RDNECcon) {
                eventcat = "UserDetails2";
                emailcon = em == '';
            } else {
                eventcat = "NameCity";
                emailcon = em == '' && (form_param.formType != 'Enq')
            }
            if (iso == 'IN') {
                if (fn == '') {
                    eventcat = eventcat + "|Name";
                }
                if (emailcon) {
                    eventcat = eventcat + "|Email";
                } 
                if (ctid == '') {
                    eventcat = eventcat + "|City";
                }
            } else {
                if (mb == '') {
                    eventcat = eventcat + "|MobileNumber";
                }
            }
            Eventtracking("DS" + window.screencount +"-"+ eventcat, state.prevtrack, form_param, false);
            dispatch({ type: 'currentscreen', payload: { currentscreen: eventcat } });
        }
    }, []);

    const handleCityChange = (cityDetails) => {
        setUserDetails(cityDetails);
          setChecks(prev => ({
        ...prev,
        citycheck: !!cityDetails.fromSuggester 
    }));
    };

    let imcntc = '';
    if (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') {
        imcntc = "imcntc";
    }

    const getAutoFocus = () => {
        if(MoreReq && focusfield){
            return focusfield;
        }
        if (fn === '' && iso === 'IN') return 'name';
        if (mb === '' && iso !== 'IN') return 'phone';
        if (em === '' && iso === 'IN') return 'email';
        if (ctid === '' && iso === 'IN') return 'city';
        return null;
    };

    const autoFocusField = getAutoFocus();

    return (
        <div className="contactdtlcls">
            {(!state.priceWidgetApiResponse && (!MoreReq && form_param.ctaType != "Image" && form_param.ctaType != "Video") && (state.openinlnBLPopup || !(form_param.formType == 'BL' && state.frscr == 1))) ?
                <Head_scr scr={"nec"} hash={form_param} /> : ""}
            {(form_param.formType == 'BL' && (state.frscr == 2 || state.openinlnBLPopup)) && (!MoreReq) && !isBlRevampd(form_param) && <div className="ber-hdg-r"><div className="blotpbl">Please provide a few details to get quotes on your mobile</div></div>}
            <form className={`contact-form ${imcntc}`}>
                {fn === '' && iso === 'IN' && <NameInput form_param={form_param} value={name} val={checks} onChange={setName} autoFocus={autoFocusField === 'name'}/>}
                {mb === '' && iso !== 'IN' && <PhoneInput form_param={form_param} value={phone} phext={phext} onChange={setPhone} warning={warning} setWarning={setWarning} autoFocus={autoFocusField === 'phone'}/>}
                {em === '' && iso === 'IN' && (MoreReq || state.RDNECcon) && <EmailInput form_param={form_param} value={email} val={checks} Blpopup={Blpopup} onChange={setEmail} autoFocus={autoFocusField === 'email'}/>}


                {ctid === '' && iso === 'IN' && <CityInput form_param={form_param} value={city} iso={iso} val={checks} onChange={setCity} onCityChange={handleCityChange} autoFocus={autoFocusField === 'city'}  setIsCityFromSuggester={setIsCityFromSuggester} validCity={validCity} setValidCity={setValidCity} />}
            </form>
            <SubmitButton userDetails={userDetails} name={name} city={city} ctid={ctid} email={email} fn={fn} em={em} mb={mb} iso={iso} mobile={phone} form_param={form_param} warning={warning} onSubmitcheck={setChecks} MoreReq={MoreReq} gst={gst} cname={cname} wUrl={wUrl} blsearchval={blsearchval} seterr_msg={seterr_msg} isCityFromSuggester={isCityFromSuggester} setValidCity={setValidCity}/>
        </div>
    );
}

export default Contactdtl;