import {  reqFormGATrackREC} from '../common/formCommfun';

export default async function FinishEnqService( postreq ,form_param) {

    try {
        const parmObj = {
            modId: form_param && form_param.modId ? form_param.modId : 'DIR',
            rfq_queryDestination:window.rfq_queryDestinationRec,
            ofr_id: postreq
        };

        const queryParams = new URLSearchParams(parmObj);
        const webAddressLocation = location.host
        const ServerName = webAddressLocation.match(/^(dev)/)? "dev-":(webAddressLocation.match(/^stg/)?"stg-":"");
        const response = await fetch(`https://${ServerName}apps.imimg.com/index.php?r=Newreqform/FinishEnqService&${queryParams}`);
        
        if (!response.ok) {
            reqFormGATrackREC("service:FinishEnqService:failure",form_param);
            throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        reqFormGATrackREC("service:FinishEnqService:success",form_param);
        return data;
        
    } catch (error) {
        // Handle errors
        reqFormGATrackREC("service:FinishEnqService:failure",form_param);
        console.error('There was a problem with the fetch operation:', error);
        imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'FinishEnqFailure','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
    }
}
