/* The main contactdtlcls container with a fixed, centered position */
.contactdtlcls {
    width: 100%;
  }
  
  
  /* Style for the modal header */
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 20px;
  }
  
  /* Close button in the top-right corner of the modal */

  
  /* Style for the form and its elements */
  .contact-form {
    display: flex;
    flex-direction: column;
    width: 331px;
    margin-top: 15px;
    margin-bottom: 10px;
  }
  
  .form-group {
    margin-bottom: 15px;
    /* margin-top: 35px; */
  }
  
  .lbl{
    padding: 0 0 9px 0;
    text-align: left;
    margin-right: 1px;
    background: 0 0;
    font-weight: 400;
    font-size: 15px;
    color: #696969;
    pointer-events: none;
    line-height: 13px;
    display: block;
  }
  
  /* Style for input fields */
  .form-group input[type="text"],
  .form-group input[type="email"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 5px;
    font-size: 15px;
    height: 42px;
  }
  .form-group input[type="text"]::placeholder,
  .form-group input[type="email"]::placeholder {
    color: #888;
    font-size: 12px;
  }
  
  .form-group input[type="text"]:focus,
  .form-group input[type="email"]:focus{
    outline: none;
    border: 1px solid #2e3192;
  }
  /* Detect city and submit buttons */
 
  .submit-button {
    width: 200px;
    height: 45px;
    border-radius: 3px;
    background-color: #029f93;
    border: solid 2px #029f93;
    font-size: 16px;
    color: #fff;
    background-color: rgb(0, 166, 153);
    cursor : pointer;
    margin-top: 10px;
  }
  .submit-button:hover {
    background-color: #007a6e !important;
    border-color: #007a6e !important;
  }
.detect-button:hover {
    text-decoration: underline;
}
.citySuggstrip{
    color: grey;
    cursor: pointer;
    padding-top: 2px;
    width: fit-content;
}
.input-error {
  border: 1px solid red !important;
}
.input-error::placeholder{
  color: red !important;
}
.ber-sugg {
  z-index: 999 !important;
}
.cityfld{
  display: flex;
  align-items: center;
}
.sugstrip{
  cursor: pointer;
  color: #2e3192;
}
.sugstrip:hover{
  text-decoration: underline;
}
.ber-hdg-r {
  font-size: 20px;
  padding: 15px 0 0 0;
  color: #000;
  line-height: 21px;
}

.blotpbl {
  color: #333;
  line-height: 21px;
  font-size: 16px;
  /* margin-bottom: 20px; */
}
.detect-button {
  position: absolute;
  right: -88px;
  top: 11px;
  color: #2e3192;
  text-decoration: underline;
  font-size: 13px;
}
.sgstx{
  color: grey;
}
.Supptxt{
  color: #777;
  font-style: italic;
  font-size: 12px;
  margin-top: 3px;
}
