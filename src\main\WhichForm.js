import React, {useEffect, useState} from 'react';
import Enq_form from './Enq_form';
import InlineEnq from '../inline_enq/inlineenq';
import { useGlobalState } from '../context/store';
import '../Login/form.css';
import '../isqform.css';
import '../inline_enq/inline.css';
import NewImgEnq_form from '../image_enq/NewImgEnq_form';
import { MakeRefTextRec, MakeRefTextRecNew, stopBgScrollREC, resumeBgScrollREC,isset,readCookieREC,callAdsGpt, imeqglval,loadimeshScriptREC, loadAdSenseScript, getparamValREC, updateimeshCombined, currentISO, getloginmode, pageViewTrackREC, isInactBL, imageabtest, wrapWithRAF } from '../common/formCommfun';
import BLpopup_form from '../BL_Popup/Bl_popup';
import mcatdtl from '../BL_Popup/McatDTLServ';
import ISQAPICall from '../ISQ/ISQAPICall';
import InlineBL from '../inlinebl/InlineBL';
import IpLoc from './Iploc';
import SessionDetailApi from '../Login/SessionDeatilApi';

function WhichForm({form_param, type , closeFn}){
    const { state, dispatch } = useGlobalState();
    const [sessionKey, setSessionKey] = useState('');
    const appsServerName = location.hostname.match(/^dev/) ? "//dev-apps.imimg.com/" : location.hostname.match(/^stg/) ? "//stg-apps.imimg.com/" : "//apps.imimg.com/";
    // form_param = (type!="inlineEnq" && type != "inlineBl") ? window.forms_param : form_param;
    // form_param = form_param == undefined ? state.Inlnformparam : form_param;
    let form_id='';

    if(isset(()=>form_param)){
        form_id =form_param.tempId+form_param.instId;
    }
    if(!window.countryiso){
        window.countryiso = currentISO();
    }
    if(form_id == "0901"){
        window.forms_param = form_param;
    }
    useEffect(() => {  
        dispatch({ type: 'nextprev', payload: { nextprev: 1 } }); 
        const fetchData = async () => {
            try {
                await ISQAPICall(form_param);
            } catch (error) {
                console.error("Error fetching ISQ data:", error);
                imgtm.push({ 'event' :  'IMEvent-NI','eventCategory' : 'Forms-Error','eventAction' : error,'eventLabel' : 'ISQAPICall','eventValue': 0, non_interaction: 0,'CD_Additional_Data' : ''});
            }
        };
        if(form_param.prodName || form_param.mcatId){
            fetchData();
        }    
    },[])
    useEffect(() => {          
        if (form_param && !imageabtest(form_param) && form_param.ctaType != "Video" && form_param.multipleImageVideo && form_param.multipleImageVideo.length > 0) {
            form_param.multipleImageVideo = form_param.multipleImageVideo.filter(
                img => !(img.type == 'Video' && (!img.vidKey || img.vidKey == "" || img.vidKey == "1" || img.vidKey == "2"))
            );
        } 
    window.screencount = 1;
    const imeqarr = imeqglval();
    if(!imeqarr.Enq && form_param.formType != 'BL'){
        window.showskipBut=true;
    }else if(!imeqarr.BL && form_param.formType == 'BL'){
        window.showskipBut=true;
    }
    else{
        window.showskipBut=false;
    }
        window.LoginmodeReact = getloginmode();   
    },[form_param])

    useEffect(() => {
        const initIpLoc = async () => {
            await IpLoc(form_param);
          };
        const iploc = isset(() => readCookieREC('iploc')) ? readCookieREC('iploc') : '';
        if(iploc == ''){
            initIpLoc();
        }
        if(form_param.tempId == "04" || form_param.tempId == "01"){
            window.forms_param = form_param;
        }else{
            stopBgScrollREC();
        }
        if(form_param.formType =="BL" && form_id == "0901"){
            const mcatdtlresponse = async () => {
                const resp = await mcatdtl(form_param);
                dispatch({ type: 'openBLPopup', payload: { openBLPopup:  resp} });
            };
            mcatdtlresponse();
        }
        window.MakeRefTextRec = MakeRefTextRec(form_param);
        window.MakeRefTextRecNew = MakeRefTextRecNew(form_param);

         let adult = isset(()=>form_param.isAdult) && form_param.isAdult !== ""
             ? form_param.isAdult
             : 2;
        if(form_param.modId=="PRODDTL"){
            if(isset(()=>window.isadsloaded) && window.isadsloaded == 0 && (parseInt(adult) === 0)){
                callAdsGpt();
            }   
            loadAdSenseScript();
        }
        
        
    },[form_param])
    
    useEffect(() => {
        window.userdata = state.UserData;
    }, [state.UserData]);
    useEffect(() => {
        const fun = async () => {
            const data =  await SessionDetailApi(getparamValREC(imesh, 'sessionKey'),form_param.modId);
            if(data && data.DataCookie){
                dispatch({ type: 'UserData', payload: { UserData: data.DataCookie } });
                setSessionKey(data.code);
            }else{
                setSessionKey("200");
            }
        };
        const imesh = readCookieREC('ImeshVisitor');
        if(imesh && getparamValREC(imesh, 'sessionKey')){
            fun();    
        }
        else if(imesh){ 
            const data=updateimeshCombined(imesh,false);
            dispatch({ type: 'UserData', payload: { UserData: data } });
            setSessionKey("200");
        }else{
            setSessionKey("200");
        }
    },[]);
    useEffect(() => {
        if(sessionKey){
            let frmtyp = '';
            if((type == "openPopup" || (form_param.tempId =="04" && state.openPopup)) && form_param.formType =="Enq"){
                if(form_param.tempId =="04" && state.openPopup){
                    frmtyp = "InlineEnquiryPopup";
                }else{
                    frmtyp = "EnquiryPopup";
                }
            }else if(type == "openImgPopup" && form_param.formType =="Enq"){
                frmtyp = "ImageEnquiryPopup";
            }else if(type == "openBLPopup" && form_param.formType =="BL" && state.openBLPopup){
                if(isInactBL(form_param)){
                    frmtyp = "InactiveBLPopup";
                }else{
                    frmtyp = "BLPopup";
                }
            }else if(state.openinlnBLPopup){
                frmtyp = "InlineBLPopup";
            }
            pageViewTrackREC(form_param,frmtyp);
        }
    },[sessionKey,form_param,type,state.openinlnBLPopup]);

    const close_Form = wrapWithRAF(() => {
        if(isset(()=>form_param) && (form_param.tempId =="04" && state.openPopup) && form_param.formType =="Enq"){
            if(isset(()=>form_param.formState)){
                form_param.formState(false);
            }
        }
        window.screencount = 1;
        dispatch({ type: "frscr", payload: { frscr: 1 } });
        dispatch({ type: 'searchshown', payload: { searchshown:  false} });
        dispatch({ type: 'currentscreen', payload: { currentscreen: '' } });
        dispatch({ type: 'prevtrack', payload: { prevtrack: "" } });

        // Reset price widget related states on form close
        dispatch({ type: 'priceWidgetApiResponse', payload: { priceWidgetApiResponse: false } });
        dispatch({ type: 'isqScreenReached', payload: { isqScreenReached: false } });

        window.isBLFormOpen = false;
        window.LoginmodeReact = getloginmode();
        window.widgetformed = false;
        resumeBgScrollREC();
        closeFn();
        dispatch({ type: 'openPopup', payload: { openPopup: false } });
        dispatch({ type: 'openinlnBLPopup', payload: { openinlnBLPopup: false } });
    });

    let url = "https://utils.imimg.com";
    if (appsServerName === "//dev-apps.imimg.com/")
        url = "https://dev-utils.imimg.com";
    else if (appsServerName === "//stg-apps.imimg.com/")
        url = "https://stg-utils.imimg.com";
    else 
        url = "https://utils.imimg.com";
    (typeof maskimesh === "function") ? "" :loadimeshScriptREC(url+"/header/scripts/imesh_mask.js");
    return(
        <>
            { type == "inlineEnq" && form_param!=="" && form_param.tempId =="04" && sessionKey ? <InlineEnq id={form_id} form_param={form_param} close_Form ={close_Form}/>:""}

            { type == "inlineBl" && form_param!=="" && form_param.tempId =="01" && sessionKey ? <InlineBL id={form_id} form_param = {window.inlineparambl || form_param}/>:""}

            { ((type == "openPopup") || (form_param.tempId =="04" && state.openPopup)) && form_param.formType =="Enq" && sessionKey ? <Enq_form id={form_id} close_fun = {close_Form} form_param={form_param}/>:""}

            { type == "openImgPopup" && form_param.formType =="Enq" && sessionKey && <NewImgEnq_form id={form_id} close_fun = {close_Form} form_param={form_param}/>}

            { type == "openBLPopup" && form_param.formType =="BL" && state.openBLPopup && sessionKey ? <BLpopup_form id={form_id} close_fun = {close_Form} form_param = {form_param} />:""}

            { state.openinlnBLPopup && sessionKey? <BLpopup_form id={form_id} close_fun = {close_Form} form_param = {window.inlineparambl || form_param}/>:""}
        </>  
         
    )
}
export default WhichForm;