import React, { useRef, useEffect, useState } from "react";
import PhEmError from "./PhEmError";
import CountrySuggester from "./CountrtDrpdwn";
import { isBlRevampd, isInlineBl } from "../common/formCommfun";
function Mobile_fld({ id, seterr_msg, err_msg, form_param, country_iso, setcountry_iso, setSelectedCountry, selectedCountry }) {
  const inputRef = useRef(null);
  const [mbl_value, setmbl_value]  = useState("");
  useEffect(() => {
    if (id == "0901" && inputRef.current) {
      inputRef.current.focus();
    }
  }, [form_param]);

  
  const handleClick = () => {
    seterr_msg("");
  };
  const handleChange = (event) => {
    setmbl_value(event.target.value);
  };
  const isNumberKey = (event) => {
    const charCode = event.which || event.keyCode;
    return charCode >= 48 && charCode <= 57;
  };
  const handleKeyPress = (event) => {
    if (!isNumberKey(event)) {
      event.preventDefault();
    }
  };
  let sbmmtcls = '';
  let lgcls = '';
  let lblcls = 'ber-lbl';
  if (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') {
    sbmmtcls = "mb15";
    lgcls = "lgwd";
  }
  if(form_param.formType == 'BL'){
    lblcls = 'prodsrchtitl';
  }
  return (
    <>
      {(id == "0901" && (form_param.ctaName == "Get Latest Price" || form_param.ctaName == "Get Free Download"  || form_param.formType == 'BL' )) ? (
        <label id={`t0901_label-l`} className={`${lblcls} beml-50`}>Mobile Number
          <span className="redc">*</span>
        </label>
      ) : null}
      
      <div className={`pr ${sbmmtcls}`}>
        {err_msg !== "" && err_msg != "Please enter your requirement" && (id == "0401") ? <PhEmError form_param={form_param} id={id} err_msg={err_msg} /> : ""}

        {isBlRevampd(form_param) ?  
        <div className='logDv'> <div id="t0901_dliso" className="cdiso">
          <input type="text" value="+91" readOnly name="iso" id="t0901_iso" className="flisq iso" tabIndex="-1" disabled />
        </div>
        <input 
          id={`t${id}_login_field_mob`} 
          type="text" 
          name="login_field" 
          value={mbl_value} 
          className={`ber-input benords beW3 inPlace pl65 grCBl ${lgcls}`} 
          placeholder="Enter your Mobile" 
          maxLength="10" 
          onClick={handleClick} 
          onChange={handleChange} 
          onKeyPress={handleKeyPress} 
          ref={inputRef} 
        /></div>
          : <>
         <div id="t0901_dliso" className="eqCntry">
          <input type="text" value="+91" readOnly name="iso" id="t0901_iso" className="flisq iso" tabIndex="-1" disabled />
        </div>
        <input 
          id={`t${id}_login_field_mob`} 
          type="text" 
          name="login_field" 
          value={mbl_value} 
          className={`ber-input benords beW3 inPlace pl65 ${lgcls}`} 
          placeholder="Enter your Mobile" 
          maxLength="10" 
          onClick={handleClick} 
          onChange={handleChange} 
          onKeyPress={handleKeyPress} 
          ref={inputRef} 
        />
        </>
        
        }
       
        {isInlineBl(form_param) && <div id="t0102_helpmsg" className="be-msghlp">Seller will contact you on this number</div>}
        <CountrySuggester country_iso={country_iso} setcountry_iso={setcountry_iso} setSelectedCountry={setSelectedCountry} selectedCountry={selectedCountry} form_param={form_param}/>
      </div>
      {err_msg !== ""&& err_msg != "Please enter your requirement" && id == "0901" ? <PhEmError form_param={form_param} id={id} err_msg={err_msg} /> : ""}
    </>
  );
}
export default Mobile_fld;