a {
  text-decoration: none;
}

.enqImg {
  min-height: 87vh;
  height: 85vh;
  /* align-self: center; */
  margin-top: 15px;
  background-color: #F6F4E0;
  border-radius: 5px 0 5px 0;
  padding: 0;
  flex-shrink: 0;

}

.ProBoxInactR {
  grid-template-columns: repeat(4, 1fr) !important;
}


.addr-city {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  margin: 6px;
  color: #5d5d5d !important;
  text-align: center;
  word-wrap: break-word;
  height: 30px;
}

.ht34 {
  height: 34px;
}

.wdnor {
  width: 654px;
}

.posunset {
  position: unset !important;
}

.imgscroll.ber-mcont {
  max-height: 97.5vh !important;
  overflow-x: hidden;
  overflow-y: auto;
}



.oEq_r.eqtstR {
  min-height: 90vh !important;
  width: 85vw !important;
  padding: 0;
  background-color: #f6f4f6;

}

.closeScrl {
  position: fixed !important;
  right: -28px !important;
  top: -15px !important;
  width: 28px !important;
  height: 28px !important;
  background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjEyMi44NzhweCIgaGVpZ2h0PSIxMjIuODhweCIgdmlld0JveD0iMCAwIDEyMi44NzggMTIyLjg4IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAxMjIuODc4IDEyMi44OCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+PHBhdGggZD0iTTEuNDI2LDguMzEzYy0xLjkwMS0xLjkwMS0xLjkwMS00Ljk4NCwwLTYuODg2YzEuOTAxLTEuOTAyLDQuOTg0LTEuOTAyLDYuODg2LDBsNTMuMTI3LDUzLjEyN2w1My4xMjctNTMuMTI3IGMxLjkwMS0xLjkwMiw0Ljk4NC0xLjkwMiw2Ljg4NywwYzEuOTAxLDEuOTAxLDEuOTAxLDQuOTg1LDAsNi44ODZMNjguMzI0LDYxLjQzOWw1My4xMjgsNTMuMTI4YzEuOTAxLDEuOTAxLDEuOTAxLDQuOTg0LDAsNi44ODYgYy0xLjkwMiwxLjkwMi00Ljk4NSwxLjkwMi02Ljg4NywwTDYxLjQzOCw2OC4zMjZMOC4zMTIsMTIxLjQ1M2MtMS45MDEsMS45MDItNC45ODQsMS45MDItNi44ODYsMCBjLTEuOTAxLTEuOTAxLTEuOTAxLTQuOTg0LDAtNi44ODZsNTMuMTI3LTUzLjEyOEwxLjQyNiw4LjMxM0wxLjQyNiw4LjMxM3oiIGZpbGw9IiNmZmZmZmYiLz48L2c+PC9zdmc+") no-repeat center center !important;
  background-color: none !important;
  background-size: 70% !important;
}

.ber-hdg-r {
  line-height: 26px;
}

.eqtstR .pdpHgr:before {
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
}

.oEq_r .pdpHgr:before {
  content: "";
  position: absolute;
  /* width: 499px; */
  /* height: 499px; */
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 1;
}

.eqtstR .pdpHgr .blrImg {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  filter: blur(3px);
  -webkit-filter: blur(3px);
  width: 100%;
  height: 100%;
}


.eqtstR.ber-mcont .pdpHgr img {
  max-height: 85vh;
}

.eqtstR.ber-mcont .pdpHgr,
.eqtstR .imgslide,
.eqtstR .pdpHgr:before,
.eqtstR .ber-Lsc.enqImg,
.eqtstR .imgslide {
  height: 85vh;
}

.oEq_r .pdpHgr img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border-radius: 3px;
  max-width: 499px;
  max-height: 499px;
}

.oEq_r .pdpHgr {
  height: 500px;
  width: 500px;
  border: 1px solid #eaeaea;
  overflow: hidden;
  border-radius: 5px;
}

/* new handlings for lower res */
/* always keep this above blrpop handlings */
.eqtstR.ber-mcont .smRes .pdpHgr {
  width: 70vh !important;
  height: 70vh !important;
}

.eqtstR.ber-mcont .smRes .pdpHgr img {
  max-height: 100% !important;
  max-width: 100% !important;
}

.eqtstR .smRes .imgslide,
.eqtstR .smRes.ber-Lsc.enqImg {
  height: 70vh !important;
  width: calc(70vh + 118px) !important;
}




.inf-scroll_new {
  transform: translate(-50%, -48.5%) !important;
  background-color: #f6f4f6;
  padding-top: 5px;
}

.eqmt10 {
  margin-top: 10px;
}

.oEq_r * {
  box-sizing: border-box;
}

.ibgc {
  background-color: #fff;
}

.sldBy {
  box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.07);
  border-radius: 8px;
  padding: 5px 5px;
  margin-bottom: 15px;
}

.epLf30 .eprod {
  font-size: 22px;
  font-weight: 700;
  color: #000;
  line-height: 2;
  padding-right: 15px;
}

.epLf30 .eprod {
  padding-top: 15px;
  padding-bottom: 0;
  font-size: 20px;
}



.eqsRt .eqflsRt {
  color: #fdc12a;
  padding: 0;
  z-index: 1;
  top: 0;
  overflow: hidden;
  bottom: 0;
}



/* ------------- Card -------------- */
.VSP-SEC * {
  margin: 0;
  padding: 0;
}

.VSP-SEC {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  background-color: #f1f1f1;
  border-radius: 8px;
}

.VSP-SEC .vs-heading {
  margin-bottom: 10px;
  margin-left: 5px;
}

.VSP-SEC .vs-heading h3 {
  font-weight: bold !important;
  font-size: 16px !important;
}

#blheading strong {
  font-weight: bold !important;
}

.ProBoxUL {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  text-align: center;
  display: flex;
  align-items: center;
}

.ProBoxUL li {
  width: 16%;
  border: 1px solid #e4e4e4;
  list-style: none;
  transition: 0.3s;
  margin: 0 5px;
}

.ProBox-Item {
  padding: 6px;
  text-decoration: none;

}

.ProBoxUL li:hover {
  box-shadow: 0 5px 17px 0 #d5d5d5;
}

.berds10 {
  border-radius: 10px;
}

.ProBox-Item .Proimg {
  height: 100px;
  width: 100px;
  margin: 0 auto;
}

.ProBox-Item .Proimg img {
  height: 100%;
  max-width: 100%;
}

.ProBox-Item .ProdList-Item-Name {
  font-size: 13px;
  text-align: center;
  margin: 8px 0;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 30px;
  line-height: 15px;
}

.ProBox-Item .proPrice {
  color: #363636;
  font-size: 13px;
  font-weight: bold;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ProBox-Item .proPrice:hover {
  color: #da2931;
}

/* ------------------ */
.ber-hdg-r1 {
  padding-top: 0;
}

.ber-hdg-r1 .otphdg {
  padding-bottom: 10px;
}

/* blotp removeclass bemb20 add class bemb8 */

.bemlsecR2 {
  margin: 6px 0 0px 0;
}

.ber-lbl2 {
  padding: 0 0 9px 0;
}

.SuggestionsCity {
  margin-top: -2px;
}

.mob-input {
  border-radius: 8px;
  height: 34px;
  width: 280px;
  padding: 0 10px;
}

.qut_cus .brdrgt8,
.brdrgt8 {
  border-radius: 0 8px 8px 0 !important;
}

.brdlft8 {
  border-radius: 8px 0 0 8px !important;
}

.color3 {
  color: #2e3192;
}

.atxu {
  text-decoration: underline;
}

.atxu:hover {
  text-decoration: none;
}

.wid_600 {
  width: 600px;
}

.bemt0 {
  margin-top: 0px;
}

.row-be2 {
  position: relative;
  margin-bottom: 20px;
}

.befstgobl {
  position: relative;
  top: -58px;
  left: 350px;
  width: 80px;
}

.bed-input {
  border-radius: 8px;
  margin: 0;
  height: 34px;
  width: 270px;
  padding: 0 10px;
}

.mt20 {
  margin-top: 20px;
}

.minH500 {
  min-height: 500px !important;
}

.orline {
  position: relative;
  color: #333;
}

.orline::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  height: 1px;
  width: 45%;
  background-color: #333;
}

.orline::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  height: 1px;
  width: 45%;
  background-color: #333;
}

.bfsbtnbl {
  cursor: pointer;
  text-align: center;
  color: #fff;
  padding: 0px 30px 0px 0px;
  border-radius: 8px;
  width: 224px;
  font-size: 20px;
  border: none;
  background-position: 148px 9px;
  line-height: 34px;
  height: 38px;
}

.bfsbtnbl:hover {
  background-color: #007a6e !important;
}

.customG2 {
  display: inline-block;
  background: #4685f5;
  color: #fff;
  width: auto;
  border-radius: 8px;
  border: thin solid #4685f5;
  box-shadow: 1px 1px 1px grey;
  white-space: nowrap;
  cursor: pointer;
  height: 38px;
}

.customG2:hover {
  background: #167ac6;
}

.bfstgobl {
  position: relative;
  top: 15px;
}

.VSP-SECI * {
  margin: 0;
  padding: 0;
}

.VSP-SECI {
  box-sizing: border-box;
  width: 100%;
  padding: 5px 10px 20px 10px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.VSP-SECI .vs-heading {
  margin-top: 10px;
  margin-bottom: 10px;
  margin-left: 5px;
}

.VSP-SECI .vs-heading h3 {
  font-weight: bold !important;
  font-size: 20px !important;
}

.ProBoxULI {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  text-align: center;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-auto-rows: 1fr;
  grid-gap: 15px;
  align-items: center;
}

.ProBoxULI li {
  width: 100%;
  border: 1px solid #e4e4e4;
  list-style: none;
  transition: 0.3s;
  word-break: break-word;
}

.ProBoxULI li:hover {
  box-shadow: 0 5px 17px 0 #d5d5d5;
}

.ProBoxULIC {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  text-align: center;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: 1fr;
  grid-gap: 10px;
  align-items: center;
}

.ProBoxULIC li {
  width: 100%;
  border: 1px solid #e4e4e4;
  list-style: none;
  transition: 0.3s;
  margin: 0 5px;
}

.ProBoxULIC li:hover {
  box-shadow: 0 5px 17px 0 #d5d5d5;
}

.img-mcats {
  display: grid;
  grid-template-columns: 100px auto;
  align-items: center;
  justify-content: flex-start;
  box-shadow: 3px 0 6px rgba(0, 0, 0, .12);
  transition: .3s;
  height: 100%;
  background-color: #fff;
  color: #2e3192;
}

.ProBox-Item .ProimgC {
  height: 100px;
  width: 100px;
  padding: 5px;
}

.ProBox-Item .ProimgC img {
  max-width: 100%;
  max-height: 100%;
}

.scrlcats {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mcard-txt {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  margin: 4px 0 4px 4px;
  padding-right: 5px;
  padding-left: 5px;
}

.mcard-txt * {
  margin: auto 0;
}

.mcard-hdg {
  font-size: 13px;
  font-weight: 700;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
  text-align: left;
}

.mcard-cty {
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  margin-top: 3px;
  color: rgba(0, 0, 0, 0.6);
  text-align: left;
  min-height: 14px;
}

.mcard-gqarw {
  margin: 0 0 -1px -3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mcard-gqarw svg {
  width: 20px;
  height: 20px
}

.hvtxu:hover {
  text-decoration: underline;
}

.eQlh {
  line-height: 24px;
}

.iClr2 {
  color: #7b7a7b;
}

.iClr1 {
  color: #313131;
}

.oEq_r.eqtstR,
.eqtstR .enqImg,
.eqtstR .ber-Rsc,
.eqtstR .bemlsecR,
.oEq_r.drqmg,
.drqmg .enqImg,
.drqmg .ber-Rsc,
.drqmg .bemlsecR {
  background-color: #f6f4f6;
}

.eqtstR .eqsold,
.eqtstR .eqsoldby {
  font-size: 16px;
  color: #000 !important;
}

.enqa.enqcmp:hover {
  text-decoration: underline;
  color: black;
}

.sClr,
.sClr:hover {
  color: #4d4d4d;
}

.eqRC2 {
  color: #707070;
}

.epf12 {
  font-size: 12px;
}

.eflwp {
  flex-wrap: wrap;
}

.eqRC3 {
  color: #0f0f10;
}

.equVs {
  background-position: -41px -25px;
}

.equVs,
.equVPs,
.equTs,
.equLs,
.equSs,
.equVe {
  width: 20px;
  height: 22px;
  margin: 0 3px 0 0;
}

.imFsp {
  background-image: url(https://apps.imimg.com/gifs/fenq-sp2.png);
  background-repeat: no-repeat;
}

@media screen and (max-width: 1515px) and (min-width: 990px) {
  .eqtstR .befs14.eqRC3 {
    font-size: 12px;
  }
}

@media screen and (max-width: 1515px) and (min-width: 990px) {
  .eqtstR .ber-Rsc .epLf30 {
    padding: 0 20px;
  }
}

#t0901_sold0R {
  color: #000 !important;
}

.eqtstR .eprod {
  padding-top: 15px;
  padding-bottom: 0;
  font-size: 20px;
}

.pl0 {
  padding-left: 0px !important;
}

.ht34 {
  height: 34px;
}

.imgscroll .ber-lbl {
  color: #05796f !important;
}

.addr-city {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  margin: 6px;
  color: #5d5d5d !important;
  text-align: center;
  word-wrap: break-word;
}

.eqsRt {
  unicode-bidi: bidi-override;
  color: #ccc;
  margin: 0;
  padding: 0;
}

.e_f18 {
  font-size: 18px;
}

.eqtstR .ber-Rsc {
  padding: 0 0 45px 0;
}


button#t0901_prebtnR,
button#t0901_nextbtnR {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer !important;
}

button#t0901_prebtnR {
  left: -50px
}

button#t0901_nextbtnR {
  right: -50px
}


#interact {
  border-radius: 0 5px 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 3;
  cursor: default;
  margin-top: 9px;
  margin-right: 9px
}

#interact button {
  background-color: rgba(0, 0, 0, 0.7);
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  border: none;
  cursor: pointer;
  padding: 7px 5px;
  margin-left: 12px;
  border-radius: 92px
}

#interact button svg,
#interact button svg path {
  fill: #fff;
}

#interact button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

#interact button[disabled] svg {
  fill: grey;
}

#t0901_zoomimage {
  transition: transform 0.3s ease;
}

.blrpopR .blurMsg {
  font-size: 23px;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: center !important;
  color: black !important;
}

.blrpopR #t0901_questionouterwrapper {
  padding: 25px !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 30vw;
  min-height: auto;
  height: auto;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.7);
}

.blrpopR .ber-cls-rec {
  position: fixed;
  z-index: 4;
}

.blrpopR .idsf.eflwp.befs14.eqRC3 {
  font-size: 13px;
}

.blrpopR iframe#videoProd {
  filter: blur(13px);
}

.blrpopR #t0901_isqdetails0R {
  display: none;
}

.blrpopR .eqRC1 {
  margin-left: 14px;
}

.blrpopR .ber-Rsc {
  padding: 0;
  position: unset;
  width: 0;
  min-height: auto;
}

.blrpopR .epLf30 {
  padding: 0 !important;
}

.blrpopR.ber-mcont .ber-Lsc.enqImg,
.blrpopR .imgslide,
.blrpopR .smRes .imgslide {
  width: 85vw !important;
}

.blrpopR.ber-mcont .pdpHgr,
.blrpopR.ber-mcont .smRes .pdpHgr {
  width: calc(85vw - 83px) !important;
}

.blrpopR .sldBy {
  box-shadow: none;
  padding: 5px 0;
  margin: 0;
  display: flex;
}

.blrpopR .eqprodpr,
.blrpopR #enqImgIsq,
.blrpopR .eprod svg,
.blrpopR #t0901_vcd {
  display: none
}

.blrpopR .eprod {
  font-size: 17px;
  line-height: 20px;
  color: #333;
  pointer-events: none;
}

.blrpopR .blurMsg {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 10px
}

.blrpopR .eqsold,
.blrpopR .eqsoldby {
  font-size: 14px;
  color: #333 !important;
  pointer-events: none;
}

.blrpopR .idsf.eflwp.befs14.eqRC3 {
  font-size: 13px;
  margin-top: -3px;
  margin-left: 15px;
}

.blrpopR #t0901_question {
  margin-top: 15px
}

.blrpopR .befstgo2 {
  font-size: 20px
}

.blrpopR .pdpHgr:before {
  background: rgba(0, 0, 0, 0.1)
}

.blrpopR .ber-Rsc {
  box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.7);
}

.blrpopR>.idsf {
  position: relative;
}

.blrpopR img#t0901_zoomimage {
  filter: blur(13px) !important;
}

.blrpopR #t0901_blurimg {
  filter: blur(23px) !important;
}

.blrpopR #t0901_sliderImg {
  filter: blur(5px) !important;
}

.blrpopR #t0901_rightproddetails {
  position: absolute;
  left: 0;
  width: 100%;
  padding: 2px 25px 12px 25px;
  top: 0;
  z-index: 3;
  min-height: auto;
  height: auto;
  background-color: #fff;
  border-radius: 0px;
  box-shadow: 0px 3px 16px rgba(0, 0, 0, 0.7);
  /* box-sizing: border-box; */
}

.cbl_vh {
  visibility: hidden;
}

.eIfvm {
  height: 98vh;
}


.eIfvm .pdpHgr {
  width: 100%;
  height: 100%;
}

.oEq_r .pdpHgr {
  width: 98%;
  height: 625px;
}

#insf-video {
  height: auto;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#inside-insf-video {
  display: flex;
  width: 370px;
  margin: 0 auto;
}

.newImgIns {
  transform: translate(-50%, -58%) !important;
}

.ifrIns {
  margin: -55px 0 -167.5px !important;
}

.blLoadR {
  border: 6px solid #a8a8a8;
  border-radius: 50%;
  border-top: 6px solid #fff;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.equTs {
  background-position: -6px -25px;
}

.bemt10 {
  margin-top: 10px;
}


.eq_img .enqLogIn.imgfv .ber-input,
.eq_img .eqIsMn .ber-input.beW3 {
  height: 44px;
  border-color: #bebcbc;
  border-radius: 6px !important;
  padding-left: 90px;
}

.epLf30 {
  padding: 0 30px;
}

.oEq_r .enqLogIn.imgfv .ber-input {
  height: 42px;
  width: 100%;
  padding-left: 70px;
  font-size: 13px;
}

.oEq_r .enqLogIn .ber-input {
  width: 390px;
  height: 50px;
  border: 1px solid #eaeaea;
  border-radius: 3px !important;
  padding-left: 70px;
}

.inactiverad {
  border-radius: 8px !important;
}

.lgwd {
  width: 100% !important;
  border-color: #bebcbc !important;
  border-radius: 6px !important;
}

.lgwd:focus {
  border-color: #00a699 !important;
}

.imcntc {
  width: 100% !important;
}

.imgDetect {
  position: relative !important;
  right: -240px !important;
  top: 0px !important
}
.imgsugg{
  position: relative;
  top: -20px;
}

.otpAlign {
  text-align: center !important;
  margin-top: 5PX !important;
}

.eqtstR .lbtool label,
.eqtstR .qt_lbl,
.eqtstR .ber-lbl {
  color: #05796f;
}

.blrpopR .bemlsecR {
  background-color: #fff;
}



.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: black;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  /* height: 100vh; */
  /* width:100vh; */
}

.back-button {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 10000;
}

.back-button {
  background: linear-gradient(to right, #eee, #ccc);
  color: black;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  font-weight: 700;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: background 0.3s ease;
  padding-left: 40px;
}

.back-button::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij4KICA8cGF0aCBkPSJNMTkgMTFINy44M2w1LjU5LTUuNTlMMTIgNGwtOCA4IDggOCAxLjQxLTEuNDFMNy44MyAxM0gxOXYtMnoiLz4KPC9zdmc+');
  background-size: contain;
  background-repeat: no-repeat;
}

div#videoProd {
  display: none;
}

.viewdetails {
  border: 1px solid #03675E;
  padding: 8px 16px;
  border-radius: 50px;
  color: #03675E;
  font-weight: bold;
  transition: 0.2s;
  margin-left: 10px;
  white-space: nowrap;
}

.viewdetails:hover {
  background: linear-gradient(180deg, #05877C 0%, #03675E 100%);
  color: white;
}

.btmar {
  margin-bottom: 10px;
}

.tpmar {
  margin-top: 10px;
}

a.eqElps.eqElps2.ber-pnm.eprod:hover {
  color: #2e368f !important;
}

.eqElps {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pr0 {
  padding-right: 0 !important;
}

.asFe {
  align-self: flex-end;
}

.Pdf_thum {
  background-image: url('https://fcp.imimg.com/gifs/pdp-sprite17.png');
  background-repeat: no-repeat;
  position: absolute;
  background-position: -335px -166px;
  width: 83px;
  height: 83px;
}

@media screen and (max-width: 1280px) and (min-width: 990px) {
  .oEq_r.eqtstR {
    width: 90vw !important;
  }

  .eqtstR.ber-mcont .pdpHgr,
  .eqtstR .imgslide,
  .eqtstR .pdpHgr:before,
  .eqtstR .ber-Lsc.enqImg,
  .eqtstR .imgslide {
    height: 80vh !important;
  }

  /* .imgwid{
    width: 800px !important;
  } */
  .blrpopR.ber-mcont .ber-Lsc.enqImg,
  .blrpopR .imgslide,
   .blrpopR .smRes .imgslide {
  width: 90vw !important;
}

}