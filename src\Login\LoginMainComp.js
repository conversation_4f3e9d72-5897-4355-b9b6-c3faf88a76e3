import React, { useState, useEffect } from "react";
import { useGlobalState } from '../context/store';
import { validpfem, identifiedcall } from './VldNmEm';
import LoginWtgoogle from './LoginWtgoogle';
import Email_fld from "./Email_fld";
import Mobile_fld from "./Mobile_fld";
import GDPRCountry, { isGDPRCountry } from "./GDPRCountry";
import LogSubmt from "./LogSubmt";
import ImgLogSubmt from '../image_enq/ImgLogSubmt';
import Ecom_BuyNow from '../image_enq/Ecom_BuyNow';
import { pageViewTrackREC, isset, Eventtracking } from '../common/formCommfun';
function LoginMainComp({ id, form_param, err_msg, seterr_msg, close_fun,blsearchval}) {
    const [name_err, setname_err] = useState("");
    const [country_iso, setcountry_iso] = useState(window.countryiso);
    const [GDPRiso, setGDPRiso] = useState(isGDPRCountry(window.countryiso));
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [isGDPRcheck, setisGDPRcheck] = useState(false);
    const [GDPRerrmsg, setGDPRerrmsg] = useState("");
    const { state, dispatch } = useGlobalState();
    useEffect(() => {
        setGDPRiso(isGDPRCountry(country_iso));
    }, [country_iso])
    useEffect(() => {
        Eventtracking("DS" + window.screencount + "-UserLogin", "" , form_param , form_param.ctaName=="Inactive" ? true : false);
        dispatch({ type: 'currentscreen', payload: { currentscreen: 'UserLogin' } });
    }, [])
    useEffect(() => {
        seterr_msg("");
        window.countryiso = country_iso;
    }, [country_iso])

    async function handlelgnSub() {
        let valid_check = validpfem(country_iso, id);
        let valid_res = valid_check.valid_res;
        let fld_val = valid_check.fld_val;
        seterr_msg(valid_res);
        if (GDPRiso && !isGDPRcheck) {
            setGDPRerrmsg("Please Agree to the Terms and Conditions");
            return false;
        }
        if (form_param.formType == "BL") {
            if(blsearchval == ""){
                seterr_msg("Please enter your requirement");
                return false;
            }
        }
        // let identresponse = (valid_res == "" ?identifiedcall(country_iso, id, fld_val, form_param, setname_err):"");
        let identresponse = "";

        if (valid_res == "") {
            identresponse = identifiedcall(country_iso, id, fld_val, form_param,seterr_msg);
        } else {
            return false;
        }
        if (identresponse instanceof Promise) {
            identresponse.then(response => {
                if (isset(() => response) && response != "") {
                    dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
                    if (response.servresp) {
                        if(response && response.DataCookie){
                        dispatch({ type: 'UserData', payload: { UserData: response.DataCookie } });}
                        dispatch({ type: "frscr", payload: { frscr: 2 } });
                        dispatch({ type: 'NECcon', payload: { NECcon: response.NECcon } });
                        dispatch({ type: 'OTPcon', payload: { OTPcon: response.OTPcon } });
                        if (form_param.formType == "BL") {
                            dispatch({ type: 'IsqformBL', payload: { IsqformBL: response.Isqform } });
                        } else {
                            dispatch({ type: 'Isqform', payload: { Isqform: response.Isqform } });
                        }
                        // response.postreq != undefined && dispatch({ type: 'postreq', payload: { postreq: response.postreq } });
                    } else if (response.servresp) {
                        seterr_msg("Authentication Failed");
                        return false;
                    }
                    else {
                        setname_err(response.vldname);
                        return false;
                    }
                    dispatch({ type: 'Imeshform', payload: { Imeshform: response.Imeshform } });
                    Eventtracking("SS" + window.screencount + "-UserLogin", state.prevtrack , form_param ,false);
                    dispatch({ type: 'prevtrack', payload: { prevtrack: "UserLogin" } });
                    window.screencount++;
                    dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep } }) ;

                }
            }).catch(error => {
                // Handle any errors that occurred in identifiedcall
                console.error(error);
                imgtm.push({ 'event': 'IMEvent-NI', 'eventCategory': 'Forms-Error', 'eventAction': error, 'eventLabel': 'Errors in identifiedcall', 'eventValue': 0, non_interaction: 0, 'CD_Additional_Data': '' });
            });
        } else if (isset(() => identresponse) && identresponse != "") {
            if (identresponse.servresp) {
                if(identresponse && identresponse.DataCookie){
                dispatch({ type: 'UserData', payload: { UserData: identresponse.DataCookie } });}
                dispatch({ type: 'NECcon', payload: { NECcon: identresponse.NECcon } });
                dispatch({ type: 'OTPcon', payload: { OTPcon: identresponse.OTPcon } });
                if (form_param.formType == "BL") {
                    dispatch({ type: 'IsqformBL', payload: { IsqformBL: identresponse.Isqform } });
                } else {
                    dispatch({ type: 'Isqform', payload: { Isqform: identresponse.Isqform } });
                }
                // response.postreq != undefined && dispatch({ type: 'postreq', payload: { postreq: identresponse.postreq } });

            } else if (identresponse.servresp) {
                seterr_msg("Authentication Failed");
                return false;
            }
            else {
                setname_err(identresponse.vldname);
                return false;
            }
            dispatch({ type: 'Imeshform', payload: { Imeshform: identresponse.Imeshform } });
            let progressstep = Number(identresponse.Imeshform) + !Number(identresponse.NECcon );
            dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep } }) ;

        }
    }
    const handleKeyPress = (event) => {
          if (event.keyCode === 13) {
            if(form_param.isEcom == 1){
                handleecomlgnSub();
            }else{
                handlelgnSub();
            } 
          }
      };
    useEffect(() => {
        document.addEventListener('keydown', handleKeyPress);
        return () => {
            document.removeEventListener('keydown', handleKeyPress);
        };
    }, [form_param,blsearchval]);
    useEffect(() => {
        window.userdata = state.UserData;
    }, [state.UserData]);
    async function handleecomlgnSub() {
        let valid_check = validpfem(country_iso, id);
        let valid_res = valid_check.valid_res;
        let fld_val = valid_check.fld_val;
        seterr_msg(valid_res);
        let identresponse = "";

        if (valid_res == "" && !(GDPRiso && !isGDPRcheck)) {
            identresponse = identifiedcall(country_iso, id, fld_val, form_param);
            window.open(form_param.ecomUrl, "_blank");
            close_fun();
        } else {
            window.open(form_param.ecomUrl, "_blank");
            close_fun();
            return false;
        }
    }
    return (
        <>
            {country_iso == "IN" ?
                <Mobile_fld id={id} seterr_msg={seterr_msg} err_msg={err_msg} form_param={form_param} country_iso={country_iso} setcountry_iso={setcountry_iso} setSelectedCountry={setSelectedCountry} selectedCountry={selectedCountry} />
                :
                <Email_fld form_param={form_param} id={id} seterr_msg={seterr_msg} setname_err={setname_err} err_msg={err_msg} name_err={name_err} country_iso={country_iso} setcountry_iso={setcountry_iso} setSelectedCountry={setSelectedCountry} selectedCountry={selectedCountry} />
            }

            {GDPRiso ? <GDPRCountry id={id} setisGDPRcheck={setisGDPRcheck} setGDPRerrmsg={setGDPRerrmsg} /> : ""}
            {GDPRerrmsg != "" && <div className="GDPRerr">{GDPRerrmsg}</div>}


            {((id == "0901" && (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') && form_param.isEcom !== 1) ?
                <ImgLogSubmt id={id} handlelgnSub={handlelgnSub} country_iso={country_iso} form_param={form_param} close_fun={close_fun} /> : (id == "0901" && (form_param.ctaType == "Image" || form_param.ctaType == "Video" || form_param.ctaType == 'pdf') && form_param.isEcom == 1) ? <Ecom_BuyNow form_param={form_param} close_fun={close_fun} handleecomlgnSub={handleecomlgnSub} /> : <LogSubmt form_param={form_param} id={id} handlelgnSub={handlelgnSub} country_iso={country_iso} err_msg={err_msg} />)}


            {country_iso == "IN" ? "" : <LoginWtgoogle handlelgnSub={form_param.isEcom == 1 ? handleecomlgnSub : handlelgnSub} id={id} country_iso={country_iso} />}

        </>
    );

}
export default LoginMainComp;




