import React, { useEffect, useRef, useState } from 'react';
import './PhoneInput.css';

const PhoneInput = ({ form_param , value, phext, onChange , warning , setWarning, autoFocus}) => {
    // const [isFocused, setIsFocused] = useState(false); 
    const handlePhoneChange = (e) => {
        const { value } = e.target;
        onChange(e.target.value);
        const isValid = /^[0-9]+$/.test(value);
        setWarning(isValid ? '' : 'Please enter only numeric characters.');
    };

    // const handleFocus = () => {
    //     setIsFocused(true);
    // };

    // const handleBlur = () => {
    //     setIsFocused(false);
    // };
    const inputRef = useRef(null);
    useEffect(() => {
        // Focus the input if autoFocus is true
        if (autoFocus && inputRef.current) {
            inputRef.current.focus();
        }
    }, [autoFocus,form_param]); // Re-run effect if autoFocus changes
    phext = phext.substring(0, 1) === "+" ? phext : "+" + phext;

    return (
        <div className="phoneParent">
            <div className="input-container">
                <label
                    htmlFor="phoneNumber"
                    // className={"active"}
                >
                    Phone Number
                </label>
                {/* <input
                    type="text"
                    name='phext'
                    value={phext}
                    readOnly
                    tabIndex="-1"
                    disabled
                /> */}
                <input
                    ref={inputRef}
                    type="text"
                    name="phoneNumber"
                    value={value}
                    maxLength='20'
                    onChange={handlePhoneChange}
                    // onFocus={handleFocus}
                    // onBlur={handleBlur}
                    placeholder='Enter Your Phone Number'
                />
            </div>
            {warning && <div style={{ color: 'red' }}>{warning}</div>}
        </div>
    );
};

export default PhoneInput;
