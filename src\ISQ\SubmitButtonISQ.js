import React, { useState, useEffect } from 'react';
import { useGlobalState } from '../context/store';
import SaveisqAPI from './SaveisqAPI';
import callPostreq from '../callpostreq';
import { isset, Eventtracking} from '../common/formCommfun';
const SubmitButtonISQ = ({ form_param, isqsLeft, handleIndexChange, selectedOptions, setSelectedOptions, cansubmit, setCansubmit, remainingisq, firstscreen, resdata ,setSelectedOptscr,selectedOptscr , blsearchval,seterr_msg}) => {
    const { state, dispatch } = useGlobalState();

    function qunatitcheck() {
        let qfil = false;
        const desc_cureent = selectedOptscr.map(opt => opt.q_desc);
        for (let index = 0; index < desc_cureent.length; index++) {
            const desc = desc_cureent[index];
            if (desc === "Quantity") {
                qfil = true;
                selectedOptscr.splice(index+1, 1);
                break;
            }   
        }
        return qfil;
    }

    const handleSubmit = async (e, identifier) => {
        e.preventDefault();
        if(form_param.formType == "BL" && blsearchval == "" && !state.openinlnBLPopup){
            seterr_msg("Please enter your requirement");
            return;
        }
        dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
        if (isset(() => cansubmit) && cansubmit == 1) {
            let temp = state.postreq;
            let trackvar = '';
            if (temp == 0) {
                let qid = await callPostreq(form_param);
                if (qid !== '') {
                    dispatch({ type: 'postreq', payload: { postreq: qid } });
                    temp = qid;
                }
            }

            if(selectedOptscr.length > 0){
                let qfil = qunatitcheck();
                if (form_param.formType == "BL") {
                    if (selectedOptscr.length >= 3) {
                        if (qfil) {
                            trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-filled-ISQ3-filled';
                        } else {
                            trackvar += 'ISQ-ISQ1-other-filled-ISQ2-filled-ISQ3-filled';
                        }
                    } else if (isqsLeft == 1 && selectedOptscr.length == 2) {
                        if (firstscreen == 'quantity') {
                            if (qfil) {
                                if (resdata == 2) {
                                    trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-filled';
                                } else if (resdata == 3) {
                                    trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-filled-ISQ3-empty';
                                }
                            } else {
                                trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-filled-ISQ3-filled';
                            }
                        } else {
                            if (resdata == 2) {
                                trackvar += 'ISQ-ISQ1-other-filled-ISQ2-filled';
                            } else if (resdata == 3) {
                                trackvar += 'ISQ-ISQ1-other-filled-ISQ2-filled-ISQ3-empty';
                            }
                        }
                    } else if (isqsLeft == 1 && selectedOptscr.length == 1) {
                        if (firstscreen == 'quantity') {
                            if (qfil) {
                                if (resdata == 1) {
                                    trackvar += 'ISQ-ISQ1-quantity-filled';
                                } else if (resdata == 2) {
                                    trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-empty';
                                } else if (resdata == 3) {
                                    trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-empty-ISQ3-empty';
                                }
                            } else {
                                if (resdata == 2) {
                                    trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-filled';
                                } else if (resdata == 3) {
                                    trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-filled-ISQ3-empty';
                                }
                            }
                        } else {
                            if (resdata == 1) {
                                trackvar += 'ISQ-ISQ1-other-filled';
                            } else if (resdata == 2) {
                                trackvar += 'ISQ-ISQ1-other-filled-ISQ2-empty';
                            } else if (resdata == 3) {
                                trackvar += 'ISQ-ISQ1-other-filled-ISQ2-empty-ISQ3-empty';
                            }
                        }
                    } else if (isqsLeft == 2) {
                        if (selectedOptscr.length == 1) {
                            if (resdata == 4) {
                                trackvar += 'ISQ-ISQ4-filled';
                            } else if (resdata == 5) {
                                trackvar += 'ISQ-ISQ4-filled-ISQ5-empty';
                            }

                        } else if (selectedOptscr.length == 2) {
                            trackvar += 'ISQ-ISQ4-filled-ISQ5-filled';
                        }
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                } else {
                    if (qfil && isqsLeft == 1) {
                        trackvar += 'ISQ-ISQ1-quantity-filled';
                    } else if (isqsLeft == 1) {
                        trackvar += 'ISQ-ISQ1-other-filled';
                    } else if (state.newEnq && selectedOptscr.length == 1 && (isqsLeft == 2 || isqsLeft == 3 || isqsLeft == 4)) {
                        trackvar += `ISQ-ISQ${isqsLeft}-filled`;
                    }

                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                }
            }else {
                if (form_param.formType == "BL") {
                    if (isqsLeft == 1) {
                        if (firstscreen == 'quantity') {
                            if (resdata == 1) {
                                trackvar += 'ISQ-ISQ1-quantity-empty';
                            } else if (resdata == 2) {
                                trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-empty';
                            } else if (resdata == 3) {
                                trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-empty-ISQ3-empty';
                            }
                        } else {
                            if (resdata == 1) {
                                trackvar += 'ISQ-ISQ1-other-empty';
                            } else if (resdata == 2) {
                                trackvar += 'ISQ-ISQ1-other-empty-ISQ2-empty';
                            } else if (resdata == 3) {
                                trackvar += 'ISQ-ISQ1-other-empty-ISQ2-empty-ISQ3-empty';
                            }
                        }
                    } else if (isqsLeft == 2) {
                        if (resdata == 4) {
                            trackvar += 'ISQ-ISQ4-empty';
                        } else if (resdata == 5) {
                            trackvar += 'ISQ-ISQ4-empty-ISQ5-empty';
                        }
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                } else {
                    if (isqsLeft == 1) {
                        trackvar += `ISQ-ISQ1-${firstscreen}-empty`;
                    } else if (state.newEnq && (isqsLeft == 2 || isqsLeft == 3 || isqsLeft == 4)) {
                        trackvar += `ISQ-ISQ${isqsLeft}-empty`;
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                }
            }
            trackvar += identifier ? "-"+identifier : "";
            Eventtracking(`SS${window.screencount}-${trackvar}`, state.prevtrack, form_param, false);
            if (selectedOptions.length > 0) {
                const b_response = selectedOptions.map(opt => opt.b_response);
                const q_desc = selectedOptions.map(opt => opt.q_desc);
                const q_id = selectedOptions.map(opt => opt.q_id);
                const b_id = selectedOptions.map(opt => opt.b_id);

                SaveisqAPI(form_param, temp, b_response, q_desc, q_id, b_id);
                setSelectedOptscr([]);
            }
            window.screencount++;
            if (form_param.formType == "BL") {
                dispatch({ type: "frscr", payload: { frscr: 2 } });
                dispatch({ type: 'IsqformBL', payload: { IsqformBL: false } });
                dispatch({ type: 'RDformBL', payload: { RDformBL: true } });
            } else {
                dispatch({ type: 'Isqform', payload: { Isqform: false } });
                dispatch({ type: 'RDform', payload: { RDform: true } });
            }

        }
        dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
    };
    const changeisqState = async (e, identifier) => {
        e.preventDefault();
        if(form_param.formType == "BL" && blsearchval == "" && !state.openinlnBLPopup){
            seterr_msg("Please enter your requirement");
            return;
        }
        dispatch({ type: 'nextprev', payload: { nextprev: 0 } });
        if (isset(() => cansubmit) && cansubmit == 1) {
            let temp = state.postreq;
            if (temp === 0) {
                let qid = await callPostreq(form_param);
                if (qid !== '') {
                    dispatch({ type: 'postreq', payload: { postreq: qid } });
                    temp = qid;
                }
            }
            let trackvar = '';
            if(selectedOptscr.length > 0){
                let qfil = qunatitcheck();
                if (form_param.formType == "BL") {
                    if (selectedOptscr.length >= 3) {
                        if (qfil) {
                            trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-filled-ISQ3-filled';
                        } else {
                            trackvar += 'ISQ-ISQ1-other-filled-ISQ2-filled-ISQ3-filled';
                        }
                    } else if (selectedOptscr.length == 2) {
                        if (firstscreen == 'quantity') {
                            if (qfil) {
                                trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-filled-ISQ3-empty';
                            } else {
                                trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-filled-ISQ3-filled';
                            }
                        } else {
                            trackvar += 'ISQ-ISQ1-other-filled-ISQ2-filled-ISQ3-empty';
                        }
                    } else if (selectedOptscr.length == 1) {
                        if (firstscreen == 'quantity') {
                            if (qfil) {
                                trackvar += 'ISQ-ISQ1-quantity-filled-ISQ2-empty-ISQ3-empty'
                            } else {
                                trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-empty-ISQ3-filled';
                            }
                        } else {
                            trackvar += 'ISQ-ISQ1-other-filled-ISQ2-empty-ISQ3-empty'
                        }
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ1` } });
                } else {
                    if (qfil && isqsLeft == 1) {
                        trackvar += 'ISQ-ISQ1-quantity-filled';
                    } else if (isqsLeft == 1) {
                        trackvar += 'ISQ-ISQ1-other-filled';
                    } else if (state.newEnq && selectedOptscr.length == 1 && (isqsLeft == 2 || isqsLeft == 3 || isqsLeft == 4)) {
                        trackvar += `ISQ-ISQ${isqsLeft}-filled`;
                    } else if (isqsLeft == 2) {
                        if (selectedOptscr.length == 1) {
                            trackvar += 'ISQ-ISQ2-filled-ISQ3-empty';
                        }
                        else if (selectedOptscr.length == 2) {
                            trackvar += 'ISQ-ISQ2-filled-ISQ3-filled';
                        }
                    }

                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                }
            } else {
                if (form_param.formType == "BL") {
                    if (firstscreen == 'quantity') {
                        trackvar += 'ISQ-ISQ1-quantity-empty-ISQ2-empty-ISQ3-empty';
                    } else {
                        trackvar += 'ISQ-ISQ1-other-empty-ISQ2-empty-ISQ3-empty';
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ1` } });
                } else {
                    if (isqsLeft == 1) {
                        trackvar += `ISQ-ISQ1-${firstscreen}-empty`;
                    } else if (state.newEnq && (isqsLeft == 2 || isqsLeft == 3 || isqsLeft == 4)) {
                        trackvar += `ISQ-ISQ${isqsLeft}-empty`;
                    } else if (isqsLeft == 2) {
                        trackvar += 'ISQ-ISQ2-empty-ISQ3-empty';
                    }
                    dispatch({ type: 'prevtrack', payload: { prevtrack: `${state.prevtrack}-ISQ${isqsLeft}` } });
                }
            }
            trackvar += identifier ? "-"+identifier : "";
            Eventtracking(`SS${window.screencount}-${trackvar}`, state.prevtrack, form_param, false);
            window.screencount++;
            if (selectedOptions.length > 0) {
                const b_response = selectedOptions.map(opt => opt.b_response);
                const q_desc = selectedOptions.map(opt => opt.q_desc);
                const q_id = selectedOptions.map(opt => opt.q_id);
                const b_id = selectedOptions.map(opt => opt.b_id);

                SaveisqAPI(form_param, temp, b_response, q_desc, q_id, b_id);
                setSelectedOptscr([]);
            }
            if (form_param.formType == "BL") {
                dispatch({ type: "frscr", payload: { frscr: 2 } });
            }
            if (state.newEnq) {
                // Remove price widget when reaching first ISQ screen
                if (isqsLeft === 1) {
                    dispatch({
                        type: "isqScreenReached",
                        payload: { isqScreenReached: true }
                    });
                    dispatch({
                        type: "priceWidgetApiResponse",
                        payload: { priceWidgetApiResponse: false }
                    });
                }
                handleIndexChange(isqsLeft);
                dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
            } else {
                if (state.progBar) {
                    dispatch({ type: 'progressstep', payload: { progressstep: state.progressstep + .15 } });
                }
                // if (isqsLeft == 1) { handleIndexChange(1); }
                // else { handleIndexChange(2); }
                handleIndexChange(isqsLeft);
            }
        }
    };

    let firstscreencond = '';
    if(form_param.formType == 'BL'){
        firstscreencond = isqsLeft == 1 && resdata>3;
    }else{
        firstscreencond = isqsLeft == 1 && resdata>1;
    }
    const handleKeyPress = (event) => {
        // if (event.keyCode === 13 && form_param.formType != 'BL' && selectedOptscr.length > 0 && selectedOptscr[0].q_desc == "Quantity") {
        if (event.keyCode === 13 && form_param.formType != 'BL' && selectedOptscr.length > 0) {
            
            
            if((isset(() => isqsLeft) && (firstscreencond || (form_param.formType != 'BL' && isqsLeft == 2))) && (remainingisq != 0)){
                changeisqState(event, 'Enter Clicked');
            }else{
                handleSubmit(event, 'Enter Clicked');
            } 
        }
    };
    useEffect(() => {
        // if (form_param.formType != 'BL' && selectedOptscr.length == 2  && selectedOptscr[0].q_desc == "Quantity") 
        if (form_param.formType != 'BL' && selectedOptscr.length > 0 ) 
        
        
            {
            document.addEventListener('keydown', handleKeyPress);
            return () => {
                document.removeEventListener('keydown', handleKeyPress);
            };
        }
    }, [selectedOptscr, form_param, isqsLeft, remainingisq,firstscreencond,blsearchval]);
    
    var clsfrm = "";
    var clsinsfrm = "submit-button";
    if (form_param.ctaType == "Image") {
        clsfrm = " form-group-img";
        clsinsfrm = "submit-button-img";
    }
    return (
        <div className={`form-group${clsfrm}`}>
            {/* {(isset(() => isqsLeft) && (firstscreencond || (form_param.formType != 'BL' && isqsLeft == 2))) && (remainingisq != 0) ? */}
             {((form_param.formType != 'BL' && remainingisq != 0) || (form_param.formType == 'BL' && remainingisq > 0) ) ?
                <button type="submit" className={clsinsfrm} onClick={(e) => changeisqState(e, '')}>
                    Next
                </button> :
                <button type="submit" className={clsinsfrm} onClick={(e) => handleSubmit(e, '')}>
                    Next
                </button>
            }
        </div>
    );
}
export default SubmitButtonISQ;